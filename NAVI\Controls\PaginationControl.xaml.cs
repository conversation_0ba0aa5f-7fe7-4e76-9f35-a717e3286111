using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace NAVI.Controls
{
    /// <summary>
    /// 分页控件
    /// </summary>
    public partial class PaginationControl : UserControl
    {
        public static readonly DependencyProperty CurrentPageProperty =
            DependencyProperty.Register("CurrentPage", typeof(int), typeof(PaginationControl), 
                new PropertyMetadata(1, OnCurrentPageChanged));

        public static readonly DependencyProperty TotalPagesProperty =
            DependencyProperty.Register("TotalPages", typeof(int), typeof(PaginationControl), 
                new PropertyMetadata(1, OnTotalPagesChanged));

        public static readonly DependencyProperty TotalRecordsProperty =
            DependencyProperty.Register("TotalRecords", typeof(int), typeof(PaginationControl), 
                new PropertyMetadata(0, OnTotalRecordsChanged));

        public static readonly DependencyProperty PageSizeProperty =
            DependencyProperty.Register("PageSize", typeof(int), typeof(PaginationControl), 
                new PropertyMetadata(10, OnPageSizeChanged));

        /// <summary>
        /// 当前页码
        /// </summary>
        public int CurrentPage
        {
            get { return (int)GetValue(CurrentPageProperty); }
            set { SetValue(CurrentPageProperty, value); }
        }

        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages
        {
            get { return (int)GetValue(TotalPagesProperty); }
            set { SetValue(TotalPagesProperty, value); }
        }

        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalRecords
        {
            get { return (int)GetValue(TotalRecordsProperty); }
            set { SetValue(TotalRecordsProperty, value); }
        }

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize
        {
            get { return (int)GetValue(PageSizeProperty); }
            set { SetValue(PageSizeProperty, value); }
        }

        /// <summary>
        /// 页码变化事件
        /// </summary>
        public event EventHandler<PageChangedEventArgs> PageChanged;

        /// <summary>
        /// 页面大小变化事件
        /// </summary>
        public event EventHandler<PageSizeChangedEventArgs> PageSizeChanged;

        public PaginationControl()
        {
            InitializeComponent();
            Loaded += PaginationControl_Loaded;
        }

        private void PaginationControl_Loaded(object sender, RoutedEventArgs e)
        {
            UpdateUI();
        }

        private static void OnCurrentPageChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            var control = d as PaginationControl;
            control?.UpdateUI();
        }

        private static void OnTotalPagesChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            var control = d as PaginationControl;
            control?.UpdateUI();
        }

        private static void OnTotalRecordsChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            var control = d as PaginationControl;
            control?.UpdateUI();
        }

        private static void OnPageSizeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            var control = d as PaginationControl;
            control?.UpdateUI();
        }

        /// <summary>
        /// 更新UI
        /// </summary>
        private void UpdateUI()
        {
            if (!IsLoaded) return;

            // 更新信息文本
            InfoTextBlock.Text = $"共{TotalRecords}条记录，第{CurrentPage}页/共{TotalPages}页";

            // 更新页面大小选择
            UpdatePageSizeSelection();

            // 更新按钮状态
            FirstPageButton.IsEnabled = CurrentPage > 1;
            PrevPageButton.IsEnabled = CurrentPage > 1;
            NextPageButton.IsEnabled = CurrentPage < TotalPages;
            LastPageButton.IsEnabled = CurrentPage < TotalPages;

            // 更新页码按钮
            UpdatePageNumbers();
        }

        /// <summary>
        /// 更新页面大小选择
        /// </summary>
        private void UpdatePageSizeSelection()
        {
            var targetPageSize = PageSize.ToString();
            foreach (ComboBoxItem item in PageSizeComboBox.Items)
            {
                if (item.Content.ToString() == targetPageSize)
                {
                    PageSizeComboBox.SelectedItem = item;
                    break;
                }
            }
        }

        /// <summary>
        /// 更新页码按钮
        /// </summary>
        private void UpdatePageNumbers()
        {
            PageNumbersPanel.Children.Clear();

            if (TotalPages <= 1) return;

            // 计算显示的页码范围
            int startPage = Math.Max(1, CurrentPage - 2);
            int endPage = Math.Min(TotalPages, CurrentPage + 2);

            // 如果开始页码大于1，显示省略号
            if (startPage > 1)
            {
                var firstButton = CreatePageButton(1);
                PageNumbersPanel.Children.Add(firstButton);

                if (startPage > 2)
                {
                    var ellipsis = new TextBlock
                    {
                        Text = "...",
                        VerticalAlignment = VerticalAlignment.Center,
                        Margin = new Thickness(8, 0, 8, 0),
                        Foreground = System.Windows.Media.Brushes.Gray
                    };
                    PageNumbersPanel.Children.Add(ellipsis);
                }
            }

            // 显示页码按钮
            for (int i = startPage; i <= endPage; i++)
            {
                var button = CreatePageButton(i);
                PageNumbersPanel.Children.Add(button);
            }

            // 如果结束页码小于总页数，显示省略号
            if (endPage < TotalPages)
            {
                if (endPage < TotalPages - 1)
                {
                    var ellipsis = new TextBlock
                    {
                        Text = "...",
                        VerticalAlignment = VerticalAlignment.Center,
                        Margin = new Thickness(8, 0, 8, 0),
                        Foreground = System.Windows.Media.Brushes.Gray
                    };
                    PageNumbersPanel.Children.Add(ellipsis);
                }

                var lastButton = CreatePageButton(TotalPages);
                PageNumbersPanel.Children.Add(lastButton);
            }
        }

        /// <summary>
        /// 创建页码按钮
        /// </summary>
        private Button CreatePageButton(int pageNumber)
        {
            var button = new Button
            {
                Content = pageNumber.ToString(),
                Tag = pageNumber,
                Style = pageNumber == CurrentPage ? 
                    (Style)FindResource("CurrentPageButtonStyle") : 
                    (Style)FindResource("PaginationButtonStyle")
            };

            button.Click += PageNumberButton_Click;
            return button;
        }

        /// <summary>
        /// 页码按钮点击事件
        /// </summary>
        private void PageNumberButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            if (button?.Tag is int pageNumber)
            {
                GoToPage(pageNumber);
            }
        }

        /// <summary>
        /// 跳转到指定页
        /// </summary>
        private void GoToPage(int pageNumber)
        {
            if (pageNumber < 1 || pageNumber > TotalPages || pageNumber == CurrentPage)
                return;

            var oldPage = CurrentPage;
            CurrentPage = pageNumber;
            
            PageChanged?.Invoke(this, new PageChangedEventArgs(oldPage, CurrentPage));
        }

        /// <summary>
        /// 首页按钮点击
        /// </summary>
        private void FirstPageButton_Click(object sender, RoutedEventArgs e)
        {
            GoToPage(1);
        }

        /// <summary>
        /// 上一页按钮点击
        /// </summary>
        private void PrevPageButton_Click(object sender, RoutedEventArgs e)
        {
            GoToPage(CurrentPage - 1);
        }

        /// <summary>
        /// 下一页按钮点击
        /// </summary>
        private void NextPageButton_Click(object sender, RoutedEventArgs e)
        {
            GoToPage(CurrentPage + 1);
        }

        /// <summary>
        /// 末页按钮点击
        /// </summary>
        private void LastPageButton_Click(object sender, RoutedEventArgs e)
        {
            GoToPage(TotalPages);
        }

        /// <summary>
        /// 跳转按钮点击
        /// </summary>
        private void JumpButton_Click(object sender, RoutedEventArgs e)
        {
            if (int.TryParse(JumpPageTextBox.Text, out int pageNumber))
            {
                GoToPage(pageNumber);
                JumpPageTextBox.Clear();
            }
        }

        /// <summary>
        /// 跳转文本框键盘事件
        /// </summary>
        private void JumpPageTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                JumpButton_Click(sender, e);
            }
        }

        /// <summary>
        /// 页面大小选择变化
        /// </summary>
        private void PageSizeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (PageSizeComboBox.SelectedItem is ComboBoxItem item && 
                int.TryParse(item.Content.ToString(), out int newPageSize))
            {
                var oldPageSize = PageSize;
                PageSize = newPageSize;
                
                PageSizeChanged?.Invoke(this, new PageSizeChangedEventArgs(oldPageSize, newPageSize));
            }
        }
    }

    /// <summary>
    /// 页码变化事件参数
    /// </summary>
    public class PageChangedEventArgs : EventArgs
    {
        public int OldPage { get; }
        public int NewPage { get; }

        public PageChangedEventArgs(int oldPage, int newPage)
        {
            OldPage = oldPage;
            NewPage = newPage;
        }
    }

    /// <summary>
    /// 页面大小变化事件参数
    /// </summary>
    public class PageSizeChangedEventArgs : EventArgs
    {
        public int OldPageSize { get; }
        public int NewPageSize { get; }

        public PageSizeChangedEventArgs(int oldPageSize, int newPageSize)
        {
            OldPageSize = oldPageSize;
            NewPageSize = newPageSize;
        }
    }
}
