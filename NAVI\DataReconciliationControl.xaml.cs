using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;

namespace NAVI
{
    /// <summary>
    /// 数据照合控件
    /// </summary>
    public partial class DataReconciliationControl : UserControl, INotifyPropertyChanged
    {
        private ObservableCollection<ReconciliationResultL> _reconciliationResults;
        private bool _isProcessing = false;

        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// 照合结果列表
        /// </summary>
        public ObservableCollection<ReconciliationResultL> ReconciliationResults
        {
            get => _reconciliationResults;
            set
            {
                _reconciliationResults = value;
                OnPropertyChanged(nameof(ReconciliationResults));
            }
        }

        public DataReconciliationControl()
        {
            InitializeComponent();
            InitializeData();
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            ReconciliationResults = new ObservableCollection<ReconciliationResultL>();
            DataContext = this;
        }

        /// <summary>
        /// 开始照合按钮点击事件
        /// </summary>
        private async void StartReconciliationButton_Click(object sender, RoutedEventArgs e)
        {
            if (MonthComboBox.SelectedItem == null)
            {
                MessageBox.Show("照合する月度を選択してください", "お知らせ",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var selectedMonth = (MonthComboBox.SelectedItem as ComboBoxItem)?.Content.ToString();
            var result = MessageBox.Show($"{selectedMonth}のデータ照合を実行してもよろしいですか？", "照合確認",
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                await StartReconciliationAsync(selectedMonth);
            }
        }

        /// <summary>
        /// 开始照合处理
        /// </summary>
        private async Task StartReconciliationAsync(string selectedMonth)
        {
            try
            {
                _isProcessing = true;
                UpdateUI();

                // 显示处理状态
                EmptyState.Visibility = Visibility.Collapsed;
                ProcessingIndicator.Visibility = Visibility.Visible;
                ProcessStatusText.Text = "処理中";

                var startTime = DateTime.Now;

                // 模拟照合处理过程
                ProcessingText.Text = "事業者データ読み込み中...";
                await Task.Delay(1000);

                ProcessingText.Text = "国保連データ読み込み中...";
                await Task.Delay(1000);

                ProcessingText.Text = "データ照合実行中...";
                await Task.Delay(2000);

                ProcessingText.Text = "結果集計中...";
                await Task.Delay(1000);

                // 生成模拟照合结果
                GenerateReconciliationResults();

                // 显示结果
                ProcessingIndicator.Visibility = Visibility.Collapsed;
                ReconciliationStats.Visibility = Visibility.Visible;

                var endTime = DateTime.Now;
                var processingTime = endTime - startTime;

                ProcessStatusText.Text = "完了";
                LastReconciliationText.Text = endTime.ToString("yyyy-MM-dd HH:mm");
                ProcessingTimeText.Text = $"{processingTime.TotalSeconds:F1}秒";

                ExportResultButton.IsEnabled = true;

                MessageBox.Show("データ照合が完了しました！", "照合完了",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"照合処理中にエラーが発生しました：{ex.Message}", "エラー",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                ProcessStatusText.Text = "エラー";
            }
            finally
            {
                _isProcessing = false;
                ProcessingIndicator.Visibility = Visibility.Collapsed;
                UpdateUI();
            }
        }

        /// <summary>
        /// 生成照合结果
        /// </summary>
        private void GenerateReconciliationResults()
        {
            ReconciliationResults.Clear();

            // 添加示例照合结果
            ReconciliationResults.Add(new ReconciliationResultL
            {
                Category = "利用者基本情報",
                ProviderCount = "1,200",
                NationalCount = "1,180",
                MatchedCount = "1,150",
                UnmatchedCount = "50",
                MatchRate = "95.8%"
            });

            ReconciliationResults.Add(new ReconciliationResultL
            {
                Category = "サービス提供記録",
                ProviderCount = "850",
                NationalCount = "820",
                MatchedCount = "780",
                UnmatchedCount = "70",
                MatchRate = "91.8%"
            });

            ReconciliationResults.Add(new ReconciliationResultL
            {
                Category = "請求情報",
                ProviderCount = "380",
                NationalCount = "430",
                MatchedCount = "250",
                UnmatchedCount = "130",
                MatchRate = "65.8%"
            });

            ReconciliationResultGrid.ItemsSource = ReconciliationResults;

            // 更新统计信息
            UpdateStatistics();
        }

        /// <summary>
        /// 更新统计信息
        /// </summary>
        private void UpdateStatistics()
        {
            var totalProvider = ReconciliationResults.Sum(r => ParseNumber(r.ProviderCount));
            var totalNational = ReconciliationResults.Sum(r => ParseNumber(r.NationalCount));
            var totalMatched = ReconciliationResults.Sum(r => ParseNumber(r.MatchedCount));
            var totalUnmatched = ReconciliationResults.Sum(r => ParseNumber(r.UnmatchedCount));

            TotalRecordsText.Text = Math.Max(totalProvider, totalNational).ToString("N0");
            MatchedRecordsText.Text = totalMatched.ToString("N0");
            UnmatchedRecordsText.Text = totalUnmatched.ToString("N0");

            var matchRate = totalMatched + totalUnmatched > 0 
                ? (double)totalMatched / (totalMatched + totalUnmatched) * 100 
                : 0;
            MatchRateText.Text = $"{matchRate:F1}%";
        }

        /// <summary>
        /// 解析数字字符串
        /// </summary>
        private int ParseNumber(string numberString)
        {
            if (int.TryParse(numberString.Replace(",", ""), out int result))
                return result;
            return 0;
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            if (_isProcessing)
            {
                var result = MessageBox.Show("照合処理をキャンセルしてもよろしいですか？", "キャンセル確認",
                    MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    _isProcessing = false;
                    ProcessingIndicator.Visibility = Visibility.Collapsed;
                    EmptyState.Visibility = Visibility.Visible;
                    ProcessStatusText.Text = "キャンセル";
                    UpdateUI();
                }
            }
            else
            {
                // 清除结果
                ReconciliationStats.Visibility = Visibility.Collapsed;
                EmptyState.Visibility = Visibility.Visible;
                ReconciliationResults.Clear();
                ProcessStatusText.Text = "待機中";
                ExportResultButton.IsEnabled = false;
                LastReconciliationText.Text = "未実行";
                ProcessingTimeText.Text = "-";
            }
        }

        /// <summary>
        /// 导出结果按钮点击事件
        /// </summary>
        private void ExportResultButton_Click(object sender, RoutedEventArgs e)
        {
            var saveFileDialog = new SaveFileDialog
            {
                Title = "导出照合结果",
                Filter = "Excel文件 (*.xlsx)|*.xlsx|CSV文件 (*.csv)|*.csv",
                DefaultExt = "xlsx",
                FileName = $"照合結果_{DateTime.Now:yyyyMMdd_HHmm}"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    // 这里应该实现实际的导出逻辑
                    MessageBox.Show($"照合結果をエクスポートしました：{saveFileDialog.FileName}", "エクスポート成功",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"エクスポートに失敗しました：{ex.Message}", "エラー",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 更新UI状态
        /// </summary>
        private void UpdateUI()
        {
            StartReconciliationButton.IsEnabled = !_isProcessing;
            MonthComboBox.IsEnabled = !_isProcessing;
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 照合结果类
    /// </summary>
    public class ReconciliationResultL
    {
        public string Category { get; set; }
        public string ProviderCount { get; set; }
        public string NationalCount { get; set; }
        public string MatchedCount { get; set; }
        public string UnmatchedCount { get; set; }
        public string MatchRate { get; set; }
    }
}
