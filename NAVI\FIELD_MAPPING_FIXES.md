# Model类字段映射修复总结

## 修复内容

### 1. 数据库表名一致性修复
- ✅ 确认KokuhoRenRepository使用正确的表名"KokuhoRenData"
- ✅ 确认RecipientRepository使用正确的表名"RecipientsData"
- ✅ 修复DatabaseService.cs中KokuhoRenData表的字段定义，统一使用双引号格式
- ✅ 修复字段名"算定時間x回数"的一致性问题

### 2. Model类与数据库字段映射完善

#### BusinessData.cs
- ✅ 添加FromServiceProvider()静态方法，从ServiceProvider实体创建BusinessData
- ✅ 添加ToServiceProvider()方法，转换为ServiceProvider实体
- ✅ 确保动态字段与数据库字段一一对应：
  - No, 事業者番号, 郵便番号, 所在地, 事業者名称, 代表者役職, 代表者名, 担当者氏名, 連絡先, サービス種別, 加算対象サービス, 第三者評価結果, 研修受講証明, 利用者情報
- ✅ 保持向后兼容性，支持旧字段名（Number, Zip, Address等）

#### NationalData.cs
- ✅ 添加FromKokuhoRenData()静态方法，从KokuhoRenData实体创建NationalData
- ✅ 添加ToKokuhoRenData()方法，转换为KokuhoRenData实体
- ✅ 确保动态字段与数据库字段一一对应：
  - No, サービス提供年月, 請求年月日, 請求回数, 審査年月, 事業者コード, 事業者名称, 受給者番号, 受給者名称, 受給者名称カナ, 児童名称, 児童名称カナ, 身体, 知的, 精神, 難病, 単価障害程度区分, 障害支援区分, サービスコード, サービス名称, 算定時間, 回数, 算定時間x回数, 単位数, サービス単位, 連合会審査区分名称, 審査区分名称, 返戻事由名称, 判定フラグ, status

#### ServiceCodeData.cs
- ✅ 添加FromServiceCodeMaster()静态方法，从ServiceCodeMaster实体创建ServiceCodeData
- ✅ 添加ToServiceCodeMaster()方法，转换为ServiceCodeMaster实体
- ✅ 确保动态字段与数据库字段一一对应：
  - サービスコード, サービス内容略称, 障害支援区分, 合成単位, 級地コード, 単位数単価, 国費単価, 旧身体療護, 都単価, キーコード, 都加算単価

#### RecipientServiceInfo.cs
- ✅ 添加FromRecipientData()静态方法，从RecipientData实体创建RecipientServiceInfo
- ✅ 添加ToRecipientData()方法，转换为RecipientData实体
- ✅ 确保动态字段与数据库字段一一对应：
  - No, 登録日, 事業者番号, 事業者郵便番号, 事業者住所, 事業者名称, 代表者名, 代表者役職, サービス提供年月, 明細書件数, 請求金額, 第三者評価, 受給者番号, 支給決定障害者氏名, 支給決定に係る障害児氏名, 障害支援区分, 事業者名称2, 地域区分, 旧身体療護施設区分, 精神科医療連携体制加算, 開始年月日, 終了年月日, 利用日数全体, サービスコード, サービス内容, 算定単価額, 利用日数, 当月算定額, 摘要, status
- ✅ 改进数据类型转换，使用TryParse避免转换异常

#### UserData.cs（新增）
- ✅ 创建新的UserData模型类
- ✅ 添加FromUser()静态方法，从User实体创建UserData
- ✅ 添加ToUser()方法，转换为User实体
- ✅ 确保动态字段与数据库字段一一对应：
  - 職員番号, 部署名, 役職, 氏名, ID番号, パスワード, 作成日時, 更新日時
- ✅ 更新项目文件包含新的UserData.cs

### 3. Repository类字段映射修复

#### KokuhoRenRepository.cs
- ✅ 修复ConvertListToDataTable方法中"算定時間×回数"字段名为"算定時間x回数"
- ✅ 修复GetColumnNames方法中字段名一致性

### 4. 数据类型安全性改进
- ✅ 在RecipientServiceInfo中使用TryParse进行安全的数据类型转换
- ✅ 避免Convert.ToInt32和Convert.ToDecimal可能引发的异常
- ✅ 为数值字段提供默认值0

## 字段映射对应关系

### 数据库表 → Model类映射
1. **ServiceProviders** → **BusinessData**
2. **KokuhoRenData** → **NationalData**  
3. **ServiceCodeMaster** → **ServiceCodeData**
4. **RecipientsData** → **RecipientServiceInfo**
5. **Users** → **UserData**

### 关键字段名修复
- ✅ "算定時間×回数" → "算定時間x回数" (统一使用x而不是×)
- ✅ 所有数据库字段名使用双引号格式统一
- ✅ Model类动态字段名与数据库字段名完全一致

## 使用方法

### 从数据库实体创建Model对象
```csharp
// 从ServiceProvider创建BusinessData
var businessData = BusinessData.FromServiceProvider(serviceProvider);

// 从KokuhoRenData创建NationalData
var nationalData = NationalData.FromKokuhoRenData(kokuhoRenData);

// 从ServiceCodeMaster创建ServiceCodeData
var serviceCodeData = ServiceCodeData.FromServiceCodeMaster(serviceCodeMaster);

// 从RecipientData创建RecipientServiceInfo
var recipientInfo = RecipientServiceInfo.FromRecipientData(recipientData);

// 从User创建UserData
var userData = UserData.FromUser(user);
```

### 从Model对象转换为数据库实体
```csharp
// 转换为数据库实体进行保存
var serviceProvider = businessData.ToServiceProvider();
var kokuhoRenData = nationalData.ToKokuhoRenData();
var serviceCodeMaster = serviceCodeData.ToServiceCodeMaster();
var recipientData = recipientInfo.ToRecipientData();
var user = userData.ToUser();
```

## 验证结果
- ✅ 所有Model类编译无错误
- ✅ 所有Repository类编译无错误
- ✅ 字段映射关系一一对应
- ✅ 数据类型转换安全可靠
- ✅ 向后兼容性保持良好
