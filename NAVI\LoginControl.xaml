﻿<UserControl x:Class="NAVI.LoginControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:NAVI"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000">
    <Grid Background="#F5F5F5">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="1*"/>
            <ColumnDefinition Width="4*"/>
        </Grid.ColumnDefinitions>
        <!-- 左側青色紹介エリア -->
        <Border Grid.Column="0" Background="#2986A8">
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center" Width="320">
                <TextBlock Text="都加算NAVI" FontSize="32" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                <TextBlock Text="【仮称】" FontSize="18" Foreground="White" HorizontalAlignment="Center" Margin="0,8,0,24"/>
                <TextBlock Foreground="White" FontSize="15" TextWrapping="Wrap">
                    地方自治体障害福祉課職員専用業務管理システム
                </TextBlock>
                <StackPanel Margin="0,16,0,0">
                    <TextBlock Foreground="White" FontSize="14" Text="・事業者申請データ自動取り込み処理"/>
                    <TextBlock Foreground="White" FontSize="14" Text="・国保連データ智能照合検証"/>
                    <TextBlock Foreground="White" FontSize="14" Text="・財務報告・補助金申請自動作成"/>
                    <TextBlock Foreground="White" FontSize="14" Text="・オフライン環境安全運用対応"/>
                </StackPanel>
            </StackPanel>
        </Border>
        <!-- 右側ログインフォームエリア -->
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center" Width="350" >
                <StackPanel Margin="0,0,0,24">
                    <TextBlock Text="ログインユーザー名" FontWeight="Bold" FontSize="15" Margin="0,0,0,4"/>
                    <TextBox Height="40" FontSize="14" Name="userName" Text="admin" HorizontalContentAlignment="Left" VerticalContentAlignment="Center"
                             BorderBrush="#DDD" BorderThickness="1" Padding="8,0"/>
                    <TextBlock Text="ログインパスワード" FontWeight="Bold"  FontSize="15" Margin="0,16,0,4"/>
                    <PasswordBox Height="40" FontSize="14" Name="passWord" Password="123456" HorizontalContentAlignment="Left" VerticalContentAlignment="Center"
                                 BorderBrush="#DDD" BorderThickness="1" Padding="8,0"/>
                    <Button Content="ログイン" Height="45" Margin="0,24,0,0" Background="#5DADE2" Foreground="White" FontWeight="Bold" FontSize="16" Click="Button_Click" BorderThickness="0"/>
                </StackPanel>
                <!-- ヘルプリンク -->
                <StackPanel Margin="0,0,0,24">
                    <TextBlock Text="ヘルプが必要な場合は以下のリンクをクリックしてください..." FontWeight="Bold" FontSize="14" Margin="0,0,0,12" HorizontalAlignment="Left"/>
                    <StackPanel>
                        <TextBlock Text="📚 操作マニュアルを確認" Foreground="#5DADE2" Cursor="Hand" Margin="0,3" FontSize="14"/>
                        <TextBlock Text="❓ よくある質問テンプレート" Foreground="#E74C3C" Cursor="Hand" Margin="0,3" FontSize="14"/>
                        <TextBlock Text="🛠 技術サポートに連絡" Foreground="#9B59B6" Cursor="Hand" Margin="0,3" FontSize="14"/>
                        <TextBlock Text="🔗 実用ツールリソース" Foreground="#95A5A6" Cursor="Hand" Margin="0,3" FontSize="14"/>
                    </StackPanel>
                </StackPanel>
            </StackPanel>
            <!-- システム入口ボタン -->
            <StackPanel Grid.Row="1" Orientation="Vertical" HorizontalAlignment="Center" Width="700" Margin="0,20,0,8">
                <TextBlock Text="自治体障害福祉課職員様へのご案内" FontWeight="Bold" FontSize="16" HorizontalAlignment="Left" Margin="0,0,0,8"/>
                <TextBlock Text="当社では障害者福祉課職員様の業務の効率化ツールを総合的に提供するシステムをご提供しております。"
                           FontSize="12" Foreground="#666" HorizontalAlignment="Left" Margin="0,0,0,16" TextWrapping="Wrap"/>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Button Grid.Row="0" Grid.Column="0" Content="障害者認定管理システム" Height="40" Margin="4" Background="#5DADE2" Foreground="White" FontSize="14" BorderThickness="0"/>
                    <Button Grid.Row="0" Grid.Column="1" Content="移動支援管理システム" Height="40" Margin="4" Background="#5DADE2" Foreground="White" FontSize="14" BorderThickness="0"/>
                    <Button Grid.Row="1" Grid.Column="0" Content="認定審査会管理システム" Height="40" Margin="4" Background="#5DADE2" Foreground="White" FontSize="14" BorderThickness="0"/>
                    <Button Grid.Row="1" Grid.Column="1" Content="その他サービス" Height="40" Margin="4" Background="#5DADE2" Foreground="White" FontSize="14" BorderThickness="0"/>
                </Grid>
            </StackPanel>
            <!-- 著作権情報 -->
            <StackPanel Grid.Row="2" VerticalAlignment="Bottom" HorizontalAlignment="Center" Margin="0,0,0,8">
                <TextBlock Text="システムバージョン ver1.105" FontSize="12" Foreground="#888" HorizontalAlignment="Center"/>
                <TextBlock Text="Copyright © 2025 terabox.co.ltd All Rights Reserved" FontSize="12" Foreground="#888" HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
