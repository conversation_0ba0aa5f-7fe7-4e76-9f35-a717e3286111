﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using NAVI.Services;
using NAVI.Services.DAL;

namespace NAVI
{
    /// <summary>
    /// LoginControl.xaml 的交互逻辑
    /// </summary>
    public partial class LoginControl : UserControl
    {
        private MainWindow _main;
        private DatabaseManager _databaseManager;
        private UserRepository _userRepository;
        private int _loginAttempts = 0;
        private const int MAX_LOGIN_ATTEMPTS = 5;
        private DateTime _lastFailedAttempt = DateTime.MinValue;

        public LoginControl(MainWindow main)
        {
            InitializeComponent();
            _main = main;

            // 初始化数据库管理器
            try
            {
                _databaseManager = new DatabaseManager();
                _userRepository = _databaseManager.Users;

                // 测试数据库连接
                TestDatabaseConnection();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"データベース接続エラー：{ex.Message}", "エラー",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 测试数据库连接
        /// </summary>
        private async void TestDatabaseConnection()
        {
            try
            {
                // 尝试获取用户数量来测试连接
                var userCount = await _databaseManager.Database.ExecuteScalarAsync("SELECT COUNT(*) FROM Users");
                System.Diagnostics.Debug.WriteLine($"数据库连接成功，用户数量：{userCount}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"数据库连接测试失败：{ex.Message}");
                MessageBox.Show($"データベース接続テストに失敗しました：{ex.Message}", "警告",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        /// <summary>
        /// 登录按钮点击事件
        /// </summary>
        private async void Button_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 检查登录尝试次数限制
                if (_loginAttempts >= MAX_LOGIN_ATTEMPTS)
                {
                    var timeSinceLastAttempt = DateTime.Now - _lastFailedAttempt;
                    if (timeSinceLastAttempt.TotalMinutes < 5) // 5分钟锁定期
                    {
                        var remainingTime = 5 - (int)timeSinceLastAttempt.TotalMinutes;
                        MessageBox.Show($"ログイン試行回数が上限に達しました。{remainingTime}分後に再試行してください。",
                            "ログイン制限", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                    else
                    {
                        // 重置尝试次数
                        _loginAttempts = 0;
                    }
                }

                // 禁用登录按钮防止重复点击
                var loginButton = sender as Button;
                if (loginButton != null)
                {
                    loginButton.IsEnabled = false;
                    loginButton.Content = "ログイン中...";
                }

                // 验证用户名和密码
                if (string.IsNullOrWhiteSpace(userName.Text))
                {
                    MessageBox.Show("ユーザー名を入力してください！", "ログイン", MessageBoxButton.OK, MessageBoxImage.Warning);
                    userName.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(passWord.Password))
                {
                    MessageBox.Show("パスワードを入力してください！", "ログイン", MessageBoxButton.OK, MessageBoxImage.Warning);
                    passWord.Focus();
                    return;
                }

                var userInput = userName.Text.Trim();
                var password = passWord.Password;

                User user = null;

                // 尝试使用职员番号登录
                user = await _userRepository.ValidateLoginAsync(userInput, password);

                // 如果职员番号登录失败，尝试使用ID番号登录
                if (user == null)
                {
                    user = await _userRepository.ValidateLoginByIdAsync(userInput, password);
                }

                if (user != null)
                {
                    // 登录成功，重置尝试次数
                    _loginAttempts = 0;

                    MessageBox.Show($"ログイン成功！ようこそ、{user.氏名}さん", "ログイン成功",
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    // 导航到主界面，传递用户信息
                    _main.NavigateToMain(user.職員番号, user);
                }
                else
                {
                    // 登录失败，增加尝试次数
                    _loginAttempts++;
                    _lastFailedAttempt = DateTime.Now;

                    var remainingAttempts = MAX_LOGIN_ATTEMPTS - _loginAttempts;
                    if (remainingAttempts > 0)
                    {
                        MessageBox.Show($"ユーザー名またはパスワードが間違っています！\n残り試行回数：{remainingAttempts}回",
                            "ログイン失敗", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                    else
                    {
                        MessageBox.Show("ログイン試行回数が上限に達しました。5分後に再試行してください。",
                            "ログイン制限", MessageBoxButton.OK, MessageBoxImage.Error);
                    }

                    passWord.Password = ""; // 清空密码
                    userName.Focus();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"ログインエラー：{ex.Message}", "エラー",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                passWord.Password = ""; // 清空密码
                userName.Focus();
            }
            finally
            {
                // 恢复登录按钮状态
                var loginButton = sender as Button;
                if (loginButton != null)
                {
                    loginButton.IsEnabled = true;
                    loginButton.Content = "ログイン";
                }
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _databaseManager?.Dispose();
        }
    }
}
