using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using NAVI.Utils;

namespace NAVI.Models
{
    /// <summary>
    /// 事业者管理数据模型
    /// </summary>
    public class BusinessData : INotifyPropertyChanged
    {
        private Dictionary<string, object> _data = new Dictionary<string, object>();


        public BusinessData()
        {
            _data = new Dictionary<string, object>();
        }

        public int No { get; set; }
        public string 事業者番号 { get; set; } = string.Empty;
        public string 郵便番号 { get; set; } = string.Empty;
        public string 所在地 { get; set; } = string.Empty;
        public string 事業者名称 { get; set; } = string.Empty;
        public string 代表者役職 { get; set; } = string.Empty;
        public string 代表者名 { get; set; } = string.Empty;
        public string 担当者氏名 { get; set; } = string.Empty;
        public string 連絡先 { get; set; } = string.Empty;
        public string サービス種別 { get; set; } = string.Empty;
        public string 加算対象サービス { get; set; } = string.Empty;
        public string 第三者評価結果 { get; set; } = string.Empty;
        public string 研修受講証明 { get; set; } = string.Empty;
        public string 利用者情報 { get; set; } = string.Empty;

        /// <summary>
        /// 动态属性访问器
        /// </summary>
        public object this[string propertyName]
        {
            get
            {
                return _data.ContainsKey(propertyName) ? _data[propertyName] : null;
            }
            set
            {
                _data[propertyName] = value;
                OnPropertyChanged(propertyName);
            }
        }

        /// <summary>
        /// 获取所有属性名
        /// </summary>
        public IEnumerable<string> PropertyNames => _data.Keys;

        /// <summary>
        /// 获取所有数据
        /// </summary>
        public Dictionary<string, object> GetAllData()
        {
            return new Dictionary<string, object>(_data);
        }

        /// <summary>
        /// 设置所有数据
        /// </summary>
        public void SetAllData(Dictionary<string, object> data)
        {
            _data = new Dictionary<string, object>(data);
            OnPropertyChanged(string.Empty); // 通知所有属性变化
        }

        /// <summary>
        /// 从ServiceProvider实体创建BusinessData
        /// </summary>
        public static BusinessData FromServiceProvider(NAVI.Services.DAL.ServiceProvider provider)
        {
            var businessData = new BusinessData();
            businessData["No"] = provider.No;
            businessData["事業者番号"] = provider.事業者番号;
            businessData["郵便番号"] = provider.郵便番号;
            businessData["所在地"] = provider.所在地;
            businessData["事業者名称"] = provider.事業者名称;
            businessData["代表者役職"] = provider.代表者役職;
            businessData["代表者名"] = provider.代表者名;
            businessData["担当者氏名"] = provider.担当者氏名;
            businessData["連絡先"] = provider.連絡先;
            businessData["サービス種別"] = provider.サービス種別;
            businessData["加算対象サービス"] = provider.加算対象サービス;
            businessData["第三者評価結果"] = provider.第三者評価結果;
            businessData["研修受講証明"] = provider.研修受講証明;
            businessData["利用者情報"] = provider.利用者情報;



            return businessData;
        }

        /// <summary>
        /// 转换为ServiceProvider实体
        /// </summary>
        public NAVI.Services.DAL.ServiceProvider ToServiceProvider()
        {
            return new NAVI.Services.DAL.ServiceProvider
            {
                No = this["No"] != null ? Convert.ToInt32(this["No"]) : 0,
                事業者番号 = this["事業者番号"]?.ToString() ?? "",
                郵便番号 = this["郵便番号"]?.ToString() ?? "",
                所在地 = this["所在地"]?.ToString() ?? "",
                事業者名称 = this["事業者名称"]?.ToString() ?? "",
                代表者役職 = this["代表者役職"]?.ToString() ?? "",
                代表者名 = this["代表者名"]?.ToString() ?? "",
                担当者氏名 = this["担当者氏名"]?.ToString() ?? "",
                連絡先 = this["連絡先"]?.ToString() ?? "",
                サービス種別 = this["サービス種別"]?.ToString() ?? "",
                加算対象サービス = this["加算対象サービス"]?.ToString() ?? "",
                第三者評価結果 = this["第三者評価結果"]?.ToString() ?? "",
                研修受講証明 = this["研修受講証明"]?.ToString() ?? "",
                利用者情報 = this["利用者情報"]?.ToString() ?? ""
            };
        }

        /// <summary>
        /// 检查是否包含属性
        /// </summary>
        public bool HasProperty(string propertyName)
        {
            return _data.ContainsKey(propertyName);
        }





        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }


}
