using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;

namespace NAVI.Models
{
    /// <summary>
    /// 国保联数据模型
    /// </summary>
    public class NationalData : INotifyPropertyChanged
    {
        private Dictionary<string, object> _data = new Dictionary<string, object>();

        public int No { get; set; }
        public string サービス提供年月 { get; set; } = string.Empty;
        public string 請求年月日 { get; set; } = string.Empty;
        public string 請求回数 { get; set; } = string.Empty;
        public string 審査年月 { get; set; } = string.Empty;
        public string 事業者コード { get; set; } = string.Empty;
        public string 事業者名称 { get; set; } = string.Empty;
        public string 受給者番号 { get; set; } = string.Empty;
        public string 受給者名称 { get; set; } = string.Empty;
        public string 受給者名称カナ { get; set; } = string.Empty;
        public string 児童名称 { get; set; } = string.Empty;
        public string 児童名称カナ { get; set; } = string.Empty;
        public string 身体 { get; set; } = string.Empty;
        public string 知的 { get; set; } = string.Empty;
        public string 精神 { get; set; } = string.Empty;
        public string 難病 { get; set; } = string.Empty;
        public string 単価障害程度区分 { get; set; } = string.Empty;
        public string 障害支援区分 { get; set; } = string.Empty;
        public string サービスコード { get; set; } = string.Empty;
        public string サービス名称 { get; set; } = string.Empty;
        public string 算定時間 { get; set; } = string.Empty;
        public string 回数 { get; set; } = string.Empty;
        public string 算定時間x回数 { get; set; } = string.Empty;
        public string 単位数 { get; set; } = string.Empty;
        public string サービス単位 { get; set; } = string.Empty;
        public string 連合会審査区分名称 { get; set; } = string.Empty;
        public string 審査区分名称 { get; set; } = string.Empty;
        public string 返戻事由名称 { get; set; } = string.Empty;
        public string 判定フラグ { get; set; } = string.Empty;
        public string status { get; set; } = string.Empty;

        /// <summary>
        /// 动态属性访问器
        /// </summary>
        public object this[string propertyName]
        {
            get
            {
                return _data.ContainsKey(propertyName) ? _data[propertyName] : null;
            }
            set
            {
                _data[propertyName] = value;
                OnPropertyChanged(propertyName);
            }
        }

        /// <summary>
        /// 获取所有属性名
        /// </summary>
        public IEnumerable<string> PropertyNames => _data.Keys;

        /// <summary>
        /// 获取所有数据
        /// </summary>
        public Dictionary<string, object> GetAllData()
        {
            return new Dictionary<string, object>(_data);
        }

        /// <summary>
        /// 设置所有数据
        /// </summary>
        public void SetAllData(Dictionary<string, object> data)
        {
            _data = new Dictionary<string, object>(data);
            OnPropertyChanged(string.Empty); // 通知所有属性变化
        }

        /// <summary>
        /// 从KokuhoRenData实体创建NationalData
        /// </summary>
        public static NationalData FromKokuhoRenData(NAVI.Services.DAL.KokuhoRenData data)
        {
            var nationalData = new NationalData();
            nationalData["No"] = data.No;
            nationalData["サービス提供年月"] = data.サービス提供年月;
            nationalData["請求年月日"] = data.請求年月日;
            nationalData["請求回数"] = data.請求回数;
            nationalData["審査年月"] = data.審査年月;
            nationalData["事業者コード"] = data.事業者コード;
            nationalData["事業者名称"] = data.事業者名称;
            nationalData["受給者番号"] = data.受給者番号;
            nationalData["受給者名称"] = data.受給者名称;
            nationalData["受給者名称カナ"] = data.受給者名称カナ;
            nationalData["児童名称"] = data.児童名称;
            nationalData["児童名称カナ"] = data.児童名称カナ;
            nationalData["身体"] = data.身体;
            nationalData["知的"] = data.知的;
            nationalData["精神"] = data.精神;
            nationalData["難病"] = data.難病;
            nationalData["単価障害程度区分"] = data.単価障害程度区分;
            nationalData["障害支援区分"] = data.障害支援区分;
            nationalData["サービスコード"] = data.サービスコード;
            nationalData["サービス名称"] = data.サービス名称;
            nationalData["算定時間"] = data.算定時間;
            nationalData["回数"] = data.回数;
            nationalData["算定時間x回数"] = data.算定時間x回数;
            nationalData["単位数"] = data.単位数;
            nationalData["サービス単位"] = data.サービス単位;
            nationalData["連合会審査区分名称"] = data.連合会審査区分名称;
            nationalData["審査区分名称"] = data.審査区分名称;
            nationalData["返戻事由名称"] = data.返戻事由名称;
            nationalData["判定フラグ"] = data.判定フラグ;
            nationalData["status"] = data.status;

            return nationalData;
        }

        /// <summary>
        /// 转换为KokuhoRenData实体
        /// </summary>
        public NAVI.Services.DAL.KokuhoRenData ToKokuhoRenData()
        {
            return new NAVI.Services.DAL.KokuhoRenData
            {
                No = this["No"] != null ? Convert.ToInt32(this["No"]) : 0,
                サービス提供年月 = this["サービス提供年月"]?.ToString() ?? "",
                請求年月日 = this["請求年月日"]?.ToString() ?? "",
                請求回数 = this["請求回数"]?.ToString() ?? "",
                審査年月 = this["審査年月"]?.ToString() ?? "",
                事業者コード = this["事業者コード"]?.ToString() ?? "",
                事業者名称 = this["事業者名称"]?.ToString() ?? "",
                受給者番号 = this["受給者番号"]?.ToString() ?? "",
                受給者名称 = this["受給者名称"]?.ToString() ?? "",
                受給者名称カナ = this["受給者名称カナ"]?.ToString() ?? "",
                児童名称 = this["児童名称"]?.ToString() ?? "",
                児童名称カナ = this["児童名称カナ"]?.ToString() ?? "",
                身体 = this["身体"]?.ToString() ?? "",
                知的 = this["知的"]?.ToString() ?? "",
                精神 = this["精神"]?.ToString() ?? "",
                難病 = this["難病"]?.ToString() ?? "",
                単価障害程度区分 = this["単価障害程度区分"]?.ToString() ?? "",
                障害支援区分 = this["障害支援区分"]?.ToString() ?? "",
                サービスコード = this["サービスコード"]?.ToString() ?? "",
                サービス名称 = this["サービス名称"]?.ToString() ?? "",
                算定時間 = this["算定時間"]?.ToString() ?? "",
                回数 = this["回数"]?.ToString() ?? "",
                算定時間x回数 = this["算定時間x回数"]?.ToString() ?? "",
                単位数 = this["単位数"]?.ToString() ?? "",
                サービス単位 = this["サービス単位"]?.ToString() ?? "",
                連合会審査区分名称 = this["連合会審査区分名称"]?.ToString() ?? "",
                審査区分名称 = this["審査区分名称"]?.ToString() ?? "",
                返戻事由名称 = this["返戻事由名称"]?.ToString() ?? "",
                判定フラグ = this["判定フラグ"]?.ToString() ?? "",
                status = this["status"]?.ToString() ?? ""
            };
        }

        /// <summary>
        /// 检查是否包含属性
        /// </summary>
        public bool HasProperty(string propertyName)
        {
            return _data.ContainsKey(propertyName);
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 受给者服务信息服务类
    /// </summary>
    public static class NationalDataService
    {
        /// <summary>
        /// 获取示例数据
        /// </summary>
        public static List<NationalData> GetSampleData()
        {
            return null;
        }

        /// <summary>
        /// 从字典列表创建受给者服务信息列表
        /// </summary>
        public static List<NationalData> CreateFromDictionaries(List<Dictionary<string, object>> dictionaries)
        {
            var result = new List<NationalData>();

            foreach (var dict in dictionaries)
            {
                var info = new NationalData();
                foreach (var kvp in dict)
                {
                    info[kvp.Key] = kvp.Value;
                }
                result.Add(info);
            }

            return result;
        }
    }
}
