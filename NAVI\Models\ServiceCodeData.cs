using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;

namespace NAVI.Models
{
    /// <summary>
    /// 服务代码数据模型
    /// </summary>
    public class ServiceCodeData : INotifyPropertyChanged
    {
        private Dictionary<string, object> _data = new Dictionary<string, object>();
        public string サービスコード { get; set; } = string.Empty;
        public string サービス内容略称 { get; set; } = string.Empty;
        public string 障害支援区分 { get; set; } = string.Empty;
        public string 合成単位 { get; set; } = string.Empty;
        public string 級地コード { get; set; } = string.Empty;
        public string 単位数単価 { get; set; } = string.Empty;
        public string 国費単価 { get; set; } = string.Empty;
        public string 旧身体療護 { get; set; } = string.Empty;
        public string 都単価 { get; set; } = string.Empty;
        public string キーコード { get; set; } = string.Empty;
        public string 都加算単価 { get; set; } = string.Empty;
        /// <summary>
        /// 动态属性访问器
        /// </summary>
        public object this[string propertyName]
        {
            get
            {
                return _data.ContainsKey(propertyName) ? _data[propertyName] : null;
            }
            set
            {
                _data[propertyName] = value;
                OnPropertyChanged(propertyName);
            }
        }

        /// <summary>
        /// 获取所有属性名
        /// </summary>
        public IEnumerable<string> PropertyNames => _data.Keys;

        /// <summary>
        /// 获取所有数据
        /// </summary>
        public Dictionary<string, object> GetAllData()
        {
            return new Dictionary<string, object>(_data);
        }

        /// <summary>
        /// 设置所有数据
        /// </summary>
        public void SetAllData(Dictionary<string, object> data)
        {
            _data = new Dictionary<string, object>(data);
            OnPropertyChanged(string.Empty); // 通知所有属性变化
        }

        /// <summary>
        /// 从ServiceCodeMaster实体创建ServiceCodeData
        /// </summary>
        public static ServiceCodeData FromServiceCodeMaster(NAVI.Services.DAL.ServiceCodeMaster master)
        {
            var serviceCodeData = new ServiceCodeData();
            serviceCodeData["サービスコード"] = master.サービスコード;
            serviceCodeData["サービス内容略称"] = master.サービス内容略称;
            serviceCodeData["障害支援区分"] = master.障害支援区分;
            serviceCodeData["合成単位"] = master.合成単位;
            serviceCodeData["級地コード"] = master.級地コード;
            serviceCodeData["単位数単価"] = master.単位数単価;
            serviceCodeData["国費単価"] = master.国費単価;
            serviceCodeData["旧身体療護"] = master.旧身体療護;
            serviceCodeData["都単価"] = master.都単価;
            serviceCodeData["キーコード"] = master.キーコード;
            serviceCodeData["都加算単価"] = master.都加算単価;

            return serviceCodeData;
        }

        /// <summary>
        /// 转换为ServiceCodeMaster实体
        /// </summary>
        public NAVI.Services.DAL.ServiceCodeMaster ToServiceCodeMaster()
        {
            return new NAVI.Services.DAL.ServiceCodeMaster
            {
                サービスコード = this["サービスコード"]?.ToString() ?? "",
                サービス内容略称 = this["サービス内容略称"]?.ToString() ?? "",
                障害支援区分 = this["障害支援区分"]?.ToString() ?? "",
                合成単位 = this["合成単位"]?.ToString() ?? "",
                級地コード = this["級地コード"]?.ToString() ?? "",
                単位数単価 = this["単位数単価"]?.ToString() ?? "",
                国費単価 = this["国費単価"]?.ToString() ?? "",
                旧身体療護 = this["旧身体療護"]?.ToString() ?? "",
                都単価 = this["都単価"]?.ToString() ?? "",
                キーコード = this["キーコード"]?.ToString() ?? "",
                都加算単価 = this["都加算単価"]?.ToString() ?? ""
            };
        }

        /// <summary>
        /// 检查是否包含属性
        /// </summary>
        public bool HasProperty(string propertyName)
        {
            return _data.ContainsKey(propertyName);
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }


}
