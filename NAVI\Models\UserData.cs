using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace NAVI.Models
{
    /// <summary>
    /// 用户数据模型
    /// </summary>
    public class UserData : INotifyPropertyChanged
    {
        private Dictionary<string, object> _data = new Dictionary<string, object>();

        /// <summary>
        /// 动态属性访问器
        /// </summary>
        public object this[string propertyName]
        {
            get
            {
                return _data.ContainsKey(propertyName) ? _data[propertyName] : null;
            }
            set
            {
                _data[propertyName] = value;
                OnPropertyChanged(propertyName);
            }
        }

        /// <summary>
        /// 获取所有属性名
        /// </summary>
        public IEnumerable<string> PropertyNames => _data.Keys;

        /// <summary>
        /// 获取所有数据
        /// </summary>
        public Dictionary<string, object> GetAllData()
        {
            return new Dictionary<string, object>(_data);
        }

        /// <summary>
        /// 设置所有数据
        /// </summary>
        public void SetAllData(Dictionary<string, object> data)
        {
            _data = new Dictionary<string, object>(data);
            OnPropertyChanged(string.Empty); // 通知所有属性变化
        }

        /// <summary>
        /// 从User实体创建UserData
        /// </summary>
        public static UserData FromUser(NAVI.Services.DAL.User user)
        {
            var userData = new UserData();
            userData["職員番号"] = user.職員番号;
            userData["部署名"] = user.部署名;
            userData["役職"] = user.役職;
            userData["氏名"] = user.氏名;
            userData["ID番号"] = user.ID番号;
            userData["パスワード"] = user.パスワード;
            userData["作成日時"] = user.作成日時;
            userData["更新日時"] = user.更新日時;
            
            return userData;
        }

        /// <summary>
        /// 转换为User实体
        /// </summary>
        public NAVI.Services.DAL.User ToUser()
        {
            return new NAVI.Services.DAL.User
            {
                職員番号 = this["職員番号"]?.ToString() ?? "",
                部署名 = this["部署名"]?.ToString() ?? "",
                役職 = this["役職"]?.ToString() ?? "",
                氏名 = this["氏名"]?.ToString() ?? "",
                ID番号 = this["ID番号"]?.ToString() ?? "",
                パスワード = this["パスワード"]?.ToString() ?? "",
                作成日時 = this["作成日時"]?.ToString() ?? "",
                更新日時 = this["更新日時"]?.ToString() ?? ""
            };
        }

        /// <summary>
        /// 检查是否包含属性
        /// </summary>
        public bool HasProperty(string propertyName)
        {
            return _data.ContainsKey(propertyName);
        }



        /// <summary>
        /// 属性变化事件
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// 触发属性变化事件
        /// </summary>
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }


    }
}
