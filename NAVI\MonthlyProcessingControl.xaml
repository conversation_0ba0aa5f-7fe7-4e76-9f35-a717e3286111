<UserControl x:Class="NAVI.MonthlyProcessingControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:NAVI"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#2E3440" Padding="15">
            <TextBlock Text="月次処理実行照合" FontSize="18" FontWeight="Bold" Foreground="White"/>
        </Border>

        <!-- 操作栏 -->
        <Border Grid.Row="1" Background="#F8F9FA" Padding="15" BorderBrush="#E9ECEF" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="処理対象月:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <DatePicker Grid.Column="1" x:Name="ProcessingDatePicker" Height="30" VerticalContentAlignment="Center"
                           SelectedDate="{Binding SelectedDate}" DisplayDateStart="2020-01-01" DisplayDateEnd="2030-12-31"/>
                
                <StackPanel Grid.Column="4" Orientation="Horizontal">
                    <Button x:Name="ExecuteButton" Content="照合実行" Width="80" Height="30" Margin="5,0" 
                            Click="ExecuteButton_Click" Style="{StaticResource PrimaryButtonStyle}"/>
                    <Button x:Name="ViewResultButton" Content="結果表示" Width="80" Height="30" Margin="5,0" 
                            Click="ViewResultButton_Click" Style="{StaticResource SecondaryButtonStyle}"/>
                    <Button x:Name="ExportButton" Content="結果出力" Width="80" Height="30" Margin="5,0" 
                            Click="ExportButton_Click" Style="{StaticResource SecondaryButtonStyle}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 结果显示区域 -->
        <Grid Grid.Row="2" Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 统计信息 -->
            <Border Grid.Row="0" Background="#E3F2FD" Padding="15" Margin="0,0,0,10" CornerRadius="5">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                        <TextBlock Text="受給者データ件数" FontWeight="Bold" HorizontalAlignment="Center"/>
                        <TextBlock x:Name="RecipientCountText" Text="0" FontSize="20" FontWeight="Bold" 
                                   HorizontalAlignment="Center" Foreground="#1976D2"/>
                    </StackPanel>

                    <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                        <TextBlock Text="国保連データ件数" FontWeight="Bold" HorizontalAlignment="Center"/>
                        <TextBlock x:Name="KokuhoRenCountText" Text="0" FontSize="20" FontWeight="Bold" 
                                   HorizontalAlignment="Center" Foreground="#1976D2"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                        <TextBlock Text="一致件数" FontWeight="Bold" HorizontalAlignment="Center"/>
                        <TextBlock x:Name="MatchCountText" Text="0" FontSize="20" FontWeight="Bold" 
                                   HorizontalAlignment="Center" Foreground="#388E3C"/>
                    </StackPanel>

                    <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                        <TextBlock Text="不一致件数" FontWeight="Bold" HorizontalAlignment="Center"/>
                        <TextBlock x:Name="MismatchCountText" Text="0" FontSize="20" FontWeight="Bold" 
                                   HorizontalAlignment="Center" Foreground="#D32F2F"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- 结果列表 -->
            <DataGrid Grid.Row="1" x:Name="ResultDataGrid" 
                      AutoGenerateColumns="False" 
                      CanUserAddRows="False" 
                      CanUserDeleteRows="False"
                      SelectionMode="Single"
                      GridLinesVisibility="All"
                      HeadersVisibility="Column"
                      AlternatingRowBackground="#F8F9FA"
                      RowBackground="White"
                      BorderBrush="#DEE2E6"
                      BorderThickness="1"
                      FontSize="11">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="No" Binding="{Binding No}" Width="60" IsReadOnly="True"/>
                    <DataGridTextColumn Header="受給者番号" Binding="{Binding 受給者番号}" Width="100" IsReadOnly="True"/>
                    <DataGridTextColumn Header="事業者番号" Binding="{Binding 事業者番号}" Width="100" IsReadOnly="True"/>
                    <DataGridTextColumn Header="サービス提供年月" Binding="{Binding サービス提供年月}" Width="120" IsReadOnly="True"/>
                    <DataGridTextColumn Header="サービスコード" Binding="{Binding サービスコード}" Width="100" IsReadOnly="True"/>
                    <DataGridTextColumn Header="サービス内容" Binding="{Binding サービス内容}" Width="150" IsReadOnly="True"/>
                    <DataGridTextColumn Header="利用日数" Binding="{Binding 利用日数}" Width="80" IsReadOnly="True"/>
                    <DataGridTextColumn Header="照合結果" Binding="{Binding 照合結果}" Width="80" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Style.Triggers>
                                    <Trigger Property="Text" Value="MATCH">
                                        <Setter Property="Background" Value="#C8E6C9"/>
                                        <Setter Property="Foreground" Value="#2E7D32"/>
                                    </Trigger>
                                    <Trigger Property="Text" Value="MISMATCH">
                                        <Setter Property="Background" Value="#FFCDD2"/>
                                        <Setter Property="Foreground" Value="#C62828"/>
                                    </Trigger>
                                    <Trigger Property="Text" Value="NOMATCH">
                                        <Setter Property="Background" Value="#FFF3E0"/>
                                        <Setter Property="Foreground" Value="#F57C00"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="更新日時" Binding="{Binding 更新日時}" Width="150" IsReadOnly="True"/>
                </DataGrid.Columns>
            </DataGrid>
        </Grid>

        <!-- 分页控件 -->
        <Border Grid.Row="3" Background="#F8F9FA" Padding="10" BorderBrush="#E9ECEF" BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" x:Name="StatusTextBlock" Text="総件数: 0 件" VerticalAlignment="Center"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="ページサイズ:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <ComboBox x:Name="PageSizeComboBox" Width="60" Height="25" SelectedIndex="2"
                              SelectionChanged="PageSizeComboBox_SelectionChanged">
                        <ComboBoxItem Content="10"/>
                        <ComboBoxItem Content="20"/>
                        <ComboBoxItem Content="50"/>
                        <ComboBoxItem Content="100"/>
                    </ComboBox>
                    
                    <Button x:Name="PrevPageButton" Content="前へ" Width="50" Height="25" Margin="10,0,5,0" 
                            Click="PrevPageButton_Click" Style="{StaticResource SecondaryButtonStyle}"/>
                    <TextBlock x:Name="PageInfoTextBlock" Text="1 / 1" VerticalAlignment="Center" Margin="10,0"/>
                    <Button x:Name="NextPageButton" Content="次へ" Width="50" Height="25" Margin="5,0,0,0" 
                            Click="NextPageButton_Click" Style="{StaticResource SecondaryButtonStyle}"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
