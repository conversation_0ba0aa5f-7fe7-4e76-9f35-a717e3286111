using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using NAVI.Services;
using NAVI.Services.DAL;

namespace NAVI
{
    /// <summary>
    /// MonthlyProcessingControl.xaml 的交互逻辑
    /// </summary>
    public partial class MonthlyProcessingControl : UserControl
    {
        private ObservableCollection<MonthlyProcessingResult> _resultList;
        private List<MonthlyProcessingResult> _allResults;
        private DatabaseManager _databaseManager;
        private RecipientRepository _recipientRepository;
        private KokuhoRenRepository _kokuhoRenRepository;

        // 分页相关
        private int _currentPage = 1;
        private int _pageSize = 50;
        private int _totalRecords = 0;

        public MonthlyProcessingControl()
        {
            InitializeComponent();
            InitializeData();
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            try
            {
                // 初始化数据库管理器
                _databaseManager = new DatabaseManager();
                _recipientRepository = _databaseManager.Recipients;
                _kokuhoRenRepository = _databaseManager.KokuhoRenData;
                
                // 设置默认日期为当前月份
                ProcessingDatePicker.SelectedDate = DateTime.Now;
                
                // 初始化结果列表
                _allResults = new List<MonthlyProcessingResult>();
                _resultList = new ObservableCollection<MonthlyProcessingResult>();
                ResultDataGrid.ItemsSource = _resultList;
                
                // 更新分页
                UpdatePagination();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化数据失败：{ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 执行照合按钮点击事件
        /// </summary>
        private async void ExecuteButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ProcessingDatePicker.SelectedDate.HasValue)
                {
                    MessageBox.Show("処理対象月を選択してください。", "入力エラー", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var selectedDate = ProcessingDatePicker.SelectedDate.Value;
                var serviceMonth = selectedDate.ToString("yyyy-MM");

                // 显示进度窗口
                var progressWindow = new Windows.ProgressWindow("月次照合処理を実行中...");
                progressWindow.Show();

                try
                {
                    // 执行月次照合处理
                    var results = await ExecuteMonthlyReconciliation(serviceMonth);
                    
                    progressWindow.Close();

                    // 更新显示
                    _allResults = results;
                    _currentPage = 1;
                    ApplyPagination();
                    UpdateStatistics();

                    MessageBox.Show($"月次照合処理が完了しました。\n処理件数: {results.Count} 件", "処理完了", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                finally
                {
                    if (progressWindow.IsVisible)
                        progressWindow.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"月次照合処理でエラーが発生しました：{ex.Message}", "エラー", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 执行月次照合处理
        /// </summary>
        private async Task<List<MonthlyProcessingResult>> ExecuteMonthlyReconciliation(string serviceMonth)
        {
            var results = new List<MonthlyProcessingResult>();

            // 获取指定月份的受给者数据（1-25日）
            var recipientData = await _recipientRepository.GetByServiceMonthAsync(serviceMonth);
            
            // 获取指定月份的国保联数据（1-25日）
            var kokuhoRenData = await _kokuhoRenRepository.GetByServiceMonthAsync(serviceMonth);

            // 创建国保联数据的查找字典
            var kokuhoRenLookup = kokuhoRenData.ToDictionary(k => 
                $"{k.受給者番号}_{k.事業者コード}_{k.サービス提供年月}_{k.サービスコード}", k => k);

            // 对每条受给者数据进行照合
            foreach (var recipient in recipientData)
            {
                var result = new MonthlyProcessingResult
                {
                    No = recipient.No,
                    受給者番号 = recipient.受給者番号,
                    事業者番号 = recipient.事業者番号,
                    サービス提供年月 = recipient.サービス提供年月,
                    サービスコード = recipient.サービスコード,
                    サービス内容 = recipient.サービス内容,
                    利用日数 = recipient.利用日数,
                    更新日時 = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };

                // 构建查找键
                var lookupKey = $"{recipient.受給者番号}_{recipient.事業者番号}_{recipient.サービス提供年月}_{recipient.サービスコード}";

                if (kokuhoRenLookup.TryGetValue(lookupKey, out var matchedKokuhoRen))
                {
                    // 找到匹配记录，比较关键字段
                    if (CompareRecords(recipient, matchedKokuhoRen))
                    {
                        result.照合結果 = "MATCH";
                        // 更新受给者数据状态为MATCH
                        await _recipientRepository.UpdateStatusAsync(recipient.No, "MATCH");
                    }
                    else
                    {
                        result.照合結果 = "MISMATCH";
                        // 更新受给者数据状态为MISMATCH
                        await _recipientRepository.UpdateStatusAsync(recipient.No, "MISMATCH");
                    }
                }
                else
                {
                    result.照合結果 = "NOMATCH";
                    // 更新受给者数据状态为NOMATCH
                    await _recipientRepository.UpdateStatusAsync(recipient.No, "NOMATCH");
                }

                results.Add(result);
            }

            return results;
        }

        /// <summary>
        /// 比较受给者数据和国保联数据的关键字段
        /// </summary>
        private bool CompareRecords(RecipientData recipient, KokuhoRenData kokuhoRen)
        {
            // 比较7个关键字段
            return recipient.受給者番号 == kokuhoRen.受給者番号 &&
                   recipient.事業者番号 == kokuhoRen.事業者コード &&
                   recipient.サービス提供年月 == kokuhoRen.サービス提供年月 &&
                   recipient.サービスコード == kokuhoRen.サービスコード &&
                   recipient.サービス内容 == kokuhoRen.サービス名称 &&
                   recipient.利用日数 == kokuhoRen.算定時間 &&
                   recipient.利用日数 == kokuhoRen.回数;
        }

        /// <summary>
        /// 应用分页
        /// </summary>
        private void ApplyPagination()
        {
            if (_allResults == null) return;

            _totalRecords = _allResults.Count;
            var totalPages = (int)Math.Ceiling((double)_totalRecords / _pageSize);
            
            if (_currentPage > totalPages && totalPages > 0)
                _currentPage = totalPages;
            if (_currentPage < 1)
                _currentPage = 1;

            var startIndex = (_currentPage - 1) * _pageSize;
            var pagedData = _allResults.Skip(startIndex).Take(_pageSize).ToList();

            _resultList.Clear();
            foreach (var item in pagedData)
            {
                _resultList.Add(item);
            }

            UpdatePagination();
        }

        /// <summary>
        /// 更新分页信息
        /// </summary>
        private void UpdatePagination()
        {
            var totalPages = (int)Math.Ceiling((double)_totalRecords / _pageSize);
            if (totalPages == 0) totalPages = 1;

            StatusTextBlock.Text = $"総件数: {_totalRecords} 件";
            PageInfoTextBlock.Text = $"{_currentPage} / {totalPages}";
            
            PrevPageButton.IsEnabled = _currentPage > 1;
            NextPageButton.IsEnabled = _currentPage < totalPages;
        }

        /// <summary>
        /// 更新统计信息
        /// </summary>
        private void UpdateStatistics()
        {
            if (_allResults == null) return;

            var matchCount = _allResults.Count(r => r.照合結果 == "MATCH");
            var mismatchCount = _allResults.Count(r => r.照合結果 == "MISMATCH");
            var noMatchCount = _allResults.Count(r => r.照合結果 == "NOMATCH");

            RecipientCountText.Text = _allResults.Count.ToString();
            KokuhoRenCountText.Text = (matchCount + mismatchCount).ToString(); // 有匹配记录的数量
            MatchCountText.Text = matchCount.ToString();
            MismatchCountText.Text = (mismatchCount + noMatchCount).ToString();
        }

        /// <summary>
        /// 结果表示按钮点击事件
        /// </summary>
        private void ViewResultButton_Click(object sender, RoutedEventArgs e)
        {
            if (_allResults == null || _allResults.Count == 0)
            {
                MessageBox.Show("表示する結果がありません。先に照合処理を実行してください。", "情報", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            ApplyPagination();
            UpdateStatistics();
        }

        /// <summary>
        /// 结果输出按钮点击事件
        /// </summary>
        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("結果出力機能は開発中です。", "情報", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 页面大小变化事件
        /// </summary>
        private void PageSizeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (PageSizeComboBox.SelectedItem is ComboBoxItem item && int.TryParse(item.Content.ToString(), out int newSize))
            {
                _pageSize = newSize;
                _currentPage = 1;
                ApplyPagination();
            }
        }

        /// <summary>
        /// 上一页按钮点击事件
        /// </summary>
        private void PrevPageButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentPage > 1)
            {
                _currentPage--;
                ApplyPagination();
            }
        }

        /// <summary>
        /// 下一页按钮点击事件
        /// </summary>
        private void NextPageButton_Click(object sender, RoutedEventArgs e)
        {
            var totalPages = (int)Math.Ceiling((double)_totalRecords / _pageSize);
            if (_currentPage < totalPages)
            {
                _currentPage++;
                ApplyPagination();
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _databaseManager?.Dispose();
        }
    }

    /// <summary>
    /// 月次处理结果
    /// </summary>
    public class MonthlyProcessingResult
    {
        public int No { get; set; }
        public string 受給者番号 { get; set; }
        public string 事業者番号 { get; set; }
        public string サービス提供年月 { get; set; }
        public string サービスコード { get; set; }
        public string サービス内容 { get; set; }
        public string 利用日数 { get; set; }
        public string 照合結果 { get; set; }
        public string 更新日時 { get; set; }
    }
}
