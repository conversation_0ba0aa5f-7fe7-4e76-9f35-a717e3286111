using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using Microsoft.Win32;
using NAVI.Services;

namespace NAVI
{
    /// <summary>
    /// 国保连CSV导入控件
    /// </summary>
    public partial class NationalCsvImportControl : UserControl, INotifyPropertyChanged
    {
        private ObservableCollection<CsvImportInfo> _importedFiles;
        private bool _isImporting = false;
        private const string ExcelFilePath = "database/shortstay_app_ver0.9.xlsx";
        private const string TargetSheetName = "国保联数据";

        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// 导入的文件列表
        /// </summary>
        public ObservableCollection<CsvImportInfo> ImportedFiles
        {
            get => _importedFiles;
            set
            {
                _importedFiles = value;
                OnPropertyChanged(nameof(ImportedFiles));
            }
        }

        public NationalCsvImportControl()
        {
            InitializeComponent();
            InitializeData();
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            ImportedFiles = new ObservableCollection<CsvImportInfo>();
            DataContext = this;

            // 确保面板初始状态为隐藏
            ImportFileListPanel.Visibility = Visibility.Collapsed;
            ProgressAndStatsPanel.Visibility = Visibility.Collapsed;
            ImportResultPanel.Visibility = Visibility.Collapsed;

            // 初始化统计信息
            UpdateStatistics();
        }

        /// <summary>
        /// 选择CSV文件按钮点击事件
        /// </summary>
        private void SelectCsvButton_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "选择CSV文件",
                Filter = "CSV文件 (*.csv)|*.csv|所有文件 (*.*)|*.*",
                CheckFileExists = true
            };

            if (openFileDialog.ShowDialog() == true)
            {
                LoadCsvFile(openFileDialog.FileName);
            }
        }

        /// <summary>
        /// 加载CSV文件
        /// </summary>
        private async void LoadCsvFile(string filePath)
        {
            try
            {
                ImportStatusText.Text = "ファイル読み込み中...";
                ImportResultPanel.Visibility = Visibility.Collapsed;

                // 读取CSV文件头部信息
                var csvInfo = await AnalyzeCsvFileAsync(filePath);

                if (csvInfo != null)
                {
                    ImportStatusText.Text = $"ファイル読み込み完了 - {csvInfo.RecordCount}レコード検出";
                    DataMappingButton.IsEnabled = true;

                    // 显示选择的文件信息
                    SelectedFileText.Text = csvInfo.FileName;
                    ProcessedRecordsText.Text = csvInfo.RecordCount;
                    SuccessCountText.Text = "-";
                    ErrorCountText.Text = "-";
                    ImportResultPanel.Visibility = Visibility.Visible;

                    // 显示导入文件列表和统计信息面板
                    ImportFileListPanel.Visibility = Visibility.Visible;
                    ProgressAndStatsPanel.Visibility = Visibility.Visible;

                    // 暂存当前文件信息
                    _currentCsvInfo = csvInfo;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"CSV文件读取失败：{ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                ImportStatusText.Text = "ファイル読み込みエラー";
                ImportResultPanel.Visibility = Visibility.Collapsed;

                // 隐藏导入文件列表和统计信息面板
                ImportFileListPanel.Visibility = Visibility.Collapsed;
                ProgressAndStatsPanel.Visibility = Visibility.Collapsed;
            }
        }

        private CsvImportInfo _currentCsvInfo;

        /// <summary>
        /// 分析CSV文件
        /// </summary>
        private async Task<CsvImportInfo> AnalyzeCsvFileAsync(string filePath)
        {
            return await Task.Run(() =>
            {
                try
                {
                    var lines = File.ReadAllLines(filePath, Encoding.UTF8);
                    if (lines.Length == 0) return null;

                    var csvInfo = new CsvImportInfo
                    {
                        FileName = Path.GetFileName(filePath),
                        FilePath = filePath,
                        ImportTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm"),
                        RecordCount = (lines.Length - 1).ToString(), // 减去标题行
                        Status = "検証待ち"
                    };

                    return csvInfo;
                }
                catch
                {
                    return null;
                }
            });
        }

        /// <summary>
        /// 数据验证按钮点击事件
        /// </summary>
        private async void DataMappingButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentCsvInfo == null)
            {
                MessageBox.Show("请先选择CSV文件", "提示",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            await ValidateDataMappingAsync();
        }

        /// <summary>
        /// 验证数据映射
        /// </summary>
        private async Task ValidateDataMappingAsync()
        {
            try
            {
                ImportStatusText.Text = "データ検証中...";
                ImportProgressBar.Visibility = Visibility.Visible;
                ImportProgressBar.IsIndeterminate = true;

                // 模拟验证过程
                await Task.Delay(2000);

                // 验证CSV字段与Excel目标表的匹配
                var validationResult = await ValidateCsvFieldsAsync(_currentCsvInfo.FilePath);

                if (validationResult)
                {
                    ImportStatusText.Text = "データ検証完了 - フィールドマッピング成功";
                    ImportExecuteButton.IsEnabled = true;
                    _currentCsvInfo.Status = "検証成功";
                }
                else
                {
                    ImportStatusText.Text = "データ検証失敗 - フィールドマッピングエラー";
                    _currentCsvInfo.Status = "検証失敗";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"数据验证失败：{ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                ImportStatusText.Text = "データ検証エラー";
            }
            finally
            {
                ImportProgressBar.Visibility = Visibility.Collapsed;
            }
        }

        /// <summary>
        /// 验证CSV字段
        /// </summary>
        private async Task<bool> ValidateCsvFieldsAsync(string filePath)
        {
            return await Task.Run(() =>
            {
                try
                {
                    // 这里应该实现实际的字段验证逻辑
                    // 读取CSV头部，与Excel目标表字段进行匹配
                    var lines = File.ReadAllLines(filePath, Encoding.UTF8);
                    if (lines.Length == 0) return false;

                    var headers = lines[0].Split(',');

                    // 模拟验证成功
                    return headers.Length > 0;
                }
                catch
                {
                    return false;
                }
            });
        }

        /// <summary>
        /// 执行导入按钮点击事件
        /// </summary>
        private async void ImportExecuteButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentCsvInfo == null)
            {
                MessageBox.Show("请先选择并验证CSV文件", "提示",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show($"确定要将CSV数据导入到Excel文件的「{TargetSheetName}」工作表吗？", "确认导入",
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                await ExecuteImportAsync();
            }
        }

        /// <summary>
        /// 执行导入
        /// </summary>
        private async Task ExecuteImportAsync()
        {
            try
            {
                _isImporting = true;
                UpdateUI();

                ImportStatusText.Text = "データ取込中...";
                ImportProgressBar.Visibility = Visibility.Visible;
                ImportProgressBar.IsIndeterminate = true;

                // 模拟导入过程
                await Task.Delay(3000);

                // 这里应该实现实际的CSV到Excel导入逻辑
                var importResult = await ImportCsvToExcelAsync(_currentCsvInfo.FilePath);

                if (importResult.Success)
                {
                    // 创建新的导入记录
                    var importRecord = new CsvImportInfo
                    {
                        FileName = _currentCsvInfo.FileName,
                        FilePath = _currentCsvInfo.FilePath,
                        ImportTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                        RecordCount = importResult.ImportedRecords.ToString(),
                        SuccessCount = importResult.SuccessCount.ToString(),
                        ErrorCount = importResult.ErrorCount.ToString(),
                        Status = "取込成功",
                        ValidationStatus = "検証済み",
                        FileSize = new FileInfo(_currentCsvInfo.FilePath).Length,
                        ProcessingTime = (long)(DateTime.Now - DateTime.Now.AddSeconds(-3)).TotalMilliseconds,
                        ImportDetails = $"処理時間: {importResult.ProcessingTime:F2}秒",
                        ErrorMessages = importResult.ErrorMessages ?? new List<string>()
                    };

                    // 添加到已导入列表（插入到列表开头，最新的在上面）
                    ImportedFiles.Insert(0, importRecord);

                    ImportStatusText.Text = $"データ取込完了 - {importResult.ImportedRecords}件のレコードを処理";

                    // 更新导入结果显示
                    ProcessedRecordsText.Text = importResult.ImportedRecords.ToString();
                    SuccessCountText.Text = importResult.SuccessCount.ToString();
                    ErrorCountText.Text = importResult.ErrorCount.ToString();

                    UpdateStatistics();

                    // 显示详细的导入结果对话框
                    var resultMessage = $"CSV取込結果:\n" +
                                      $"ファイル名: {_currentCsvInfo.FileName}\n" +
                                      $"処理レコード数: {importResult.ImportedRecords}件\n" +
                                      $"成功: {importResult.SuccessCount}件\n" +
                                      $"スキップ: {importResult.SkippedCount}件\n" +
                                      $"エラー: {importResult.ErrorCount}件\n" +
                                      $"処理時間: {importResult.ProcessingTime:F2}秒";

                    if (importResult.ColumnMappings.Count > 0)
                    {
                        resultMessage += "\n\nカラムマッピング:\n";
                        foreach (var mapping in importResult.ColumnMappings)
                        {
                            if (!string.IsNullOrEmpty(mapping.Value))
                            {
                                resultMessage += $"  {mapping.Key} → {mapping.Value}\n";
                            }
                        }
                    }

                    // 显示确认操作对话框
                    var confirmResult = MessageBox.Show(
                        resultMessage + "\n\n取込を確定しますか？",
                        "取込完了 - 確認操作",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (confirmResult == MessageBoxResult.Yes)
                    {
                        MessageBox.Show("データ取込が確定されました。", "確定完了",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                else
                {
                    // 创建失败的导入记录
                    var importRecord = new CsvImportInfo
                    {
                        FileName = _currentCsvInfo.FileName,
                        FilePath = _currentCsvInfo.FilePath,
                        ImportTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                        RecordCount = importResult.ImportedRecords.ToString(),
                        SuccessCount = importResult.SuccessCount.ToString(),
                        ErrorCount = importResult.ErrorCount.ToString(),
                        Status = "取込失敗",
                        ValidationStatus = "検証エラー",
                        FileSize = new FileInfo(_currentCsvInfo.FilePath).Length,
                        ProcessingTime = (long)(DateTime.Now - DateTime.Now.AddSeconds(-3)).TotalMilliseconds,
                        ImportDetails = $"エラー: {importResult.ErrorMessage}",
                        ErrorMessages = importResult.ErrorMessages ?? new List<string> { importResult.ErrorMessage }
                    };

                    // 添加到已导入列表（插入到列表开头）
                    ImportedFiles.Insert(0, importRecord);

                    ImportStatusText.Text = $"データ取込失敗 - {importResult.ErrorMessage}";

                    // 更新导入结果显示
                    ProcessedRecordsText.Text = importResult.ImportedRecords.ToString();
                    SuccessCountText.Text = importResult.SuccessCount.ToString();
                    ErrorCountText.Text = importResult.ErrorCount.ToString();

                    UpdateStatistics();

                    MessageBox.Show($"CSV取込エラー:\n{importResult.ErrorMessage}", "取込失敗",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }

                // 重置状态
                _currentCsvInfo = null;
                DataMappingButton.IsEnabled = false;
                ImportExecuteButton.IsEnabled = false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导入过程中发生错误：{ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                ImportStatusText.Text = "データ取込エラー";
            }
            finally
            {
                _isImporting = false;
                ImportProgressBar.Visibility = Visibility.Collapsed;
                UpdateUI();
            }
        }

        /// <summary>
        /// 导入CSV到Excel
        /// </summary>
        private async Task<CsvImportResult> ImportCsvToExcelAsync(string csvFilePath)
        {
            return await Task.Run(() =>
            {
                var result = new CsvImportResult();
                var startTime = DateTime.Now;

                try
                {
                    // 1. 读取CSV数据
                    var csvData = ReadCsvFile(csvFilePath);
                    if (csvData.Count == 0)
                    {
                        result.ErrorMessage = "CSVファイルが空またはデータが読み取れません";
                        return result;
                    }

                    result.ImportedRecords = csvData.Count;

                    // 2. 打开Excel文件
                    var fullExcelPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, ExcelFilePath);
                    if (!File.Exists(fullExcelPath))
                    {
                        result.ErrorMessage = "Excelファイルが見つかりません: " + fullExcelPath;
                        return result;
                    }

                    var excelService = new ExcelDataService(fullExcelPath);

                    // 3. 获取目标工作表的列名
                    var excelColumns = excelService.GetSheetColumns(TargetSheetName);

                    // 获取当前最大行号
                    var existingData = excelService.GetSheetData("国保联数据");
                    int nextRowNumber = existingData.Count + 1;
                    if (excelColumns.Count == 0)
                    {
                        result.ErrorMessage = $"Excelシート '{TargetSheetName}' が見つからないか、カラムが取得できません";
                        return result;
                    }

                    // 4. 获取CSV的列名（第一行）
                    var csvColumns = csvData[0].Keys.ToList();

                    // 5. 创建列名映射
                    var columnMapping = CreateColumnMapping(csvColumns, excelColumns);
                    result.ColumnMappings = columnMapping;

                    // 6. 转换CSV数据为Excel格式
                    var excelData = ConvertCsvDataToExcelFormat(csvData, columnMapping, excelColumns);

                    // 7. 批量导入到Excel
                    int successCount = 0;
                    int errorCount = 0;

                    foreach (var rowData in excelData)
                    {
                        try
                        {
                            //替换No行号
                            rowData["No"] = nextRowNumber;
                            excelService.AddRow(TargetSheetName, rowData);
                            successCount++;
                            nextRowNumber++;
                        }
                        catch (Exception rowEx)
                        {
                            errorCount++;
                            System.Diagnostics.Debug.WriteLine($"行データ追加エラー: {rowEx.Message}");
                        }
                    }

                    // 8. 保存Excel文件
                    excelService.Save();

                    // 设置结果统计
                    result.Success = true;
                    result.SuccessCount = successCount;
                    result.ErrorCount = errorCount;
                    result.SkippedCount = result.ImportedRecords - successCount - errorCount;
                    result.ProcessingTime = (DateTime.Now - startTime).TotalSeconds;

                    return result;
                }
                catch (Exception ex)
                {
                    result.ErrorMessage = $"CSV取込処理中にエラーが発生しました: {ex.Message}";
                    result.ProcessingTime = (DateTime.Now - startTime).TotalSeconds;
                    System.Diagnostics.Debug.WriteLine($"CSV导入失败: {ex.Message}");
                    return result;
                }
            });
        }

        /// <summary>
        /// 读取CSV文件
        /// </summary>
        private List<Dictionary<string, object>> ReadCsvFile(string csvFilePath)
        {
            var result = new List<Dictionary<string, object>>();

            try
            {
                var lines = File.ReadAllLines(csvFilePath, Encoding.UTF8);
                if (lines.Length == 0) return result;

                // 获取CSV头部
                var headers = ParseCsvLine(lines[0]);
                if (headers.Length == 0) return result;

                // 读取数据行
                for (int i = 1; i < lines.Length; i++)
                {
                    var values = ParseCsvLine(lines[i]);
                    if (values.Length == 0) continue;

                    var rowData = new Dictionary<string, object>();
                    for (int j = 0; j < headers.Length; j++)
                    {
                        var value = j < values.Length ? values[j] : "";
                        rowData[headers[j]] = value;
                    }

                    result.Add(rowData);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"读取CSV文件失败: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 解析CSV行
        /// </summary>
        private string[] ParseCsvLine(string line)
        {
            if (string.IsNullOrEmpty(line)) return new string[0];

            var result = new List<string>();
            var current = new StringBuilder();
            bool inQuotes = false;

            for (int i = 0; i < line.Length; i++)
            {
                char c = line[i];

                if (c == '"')
                {
                    inQuotes = !inQuotes;
                }
                else if (c == ',' && !inQuotes)
                {
                    result.Add(current.ToString().Trim());
                    current.Clear();
                }
                else
                {
                    current.Append(c);
                }
            }

            result.Add(current.ToString().Trim());
            return result.ToArray();
        }

        /// <summary>
        /// 创建列名映射
        /// </summary>
        private Dictionary<string, string> CreateColumnMapping(List<string> csvColumns, List<string> excelColumns)
        {
            var mapping = new Dictionary<string, string>();

            foreach (var csvColumn in csvColumns)
            {
                // 尝试精确匹配
                var exactMatch = excelColumns.FirstOrDefault(ec => ec.Equals(csvColumn, StringComparison.OrdinalIgnoreCase));
                if (exactMatch != null)
                {
                    mapping[csvColumn] = exactMatch;
                    continue;
                }

                // 尝试部分匹配
                var partialMatch = excelColumns.FirstOrDefault(ec =>
                    ec.Contains(csvColumn) || csvColumn.Contains(ec));
                if (partialMatch != null)
                {
                    mapping[csvColumn] = partialMatch;
                    continue;
                }

                // 尝试常见的字段映射
                var commonMapping = GetCommonFieldMapping(csvColumn);
                if (!string.IsNullOrEmpty(commonMapping) && excelColumns.Contains(commonMapping))
                {
                    mapping[csvColumn] = commonMapping;
                    continue;
                }

                // 如果没有匹配，则映射为null（将被置为空）
                mapping[csvColumn] = null;
            }

            return mapping;
        }

        /// <summary>
        /// 获取常见字段映射
        /// </summary>
        private string GetCommonFieldMapping(string csvColumn)
        {
            var commonMappings = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
            {
                // 日文到中文映射
                {"保険者番号", "保险者番号"},
                {"被保険者番号", "被保险者番号"},
                {"氏名", "氏名"},
                {"生年月日", "生年月日"},
                {"性別", "性别"},
                {"住所", "住所"},
                {"電話番号", "电话番号"},
                {"認定年月日", "认定年月日"},
                {"有効期限", "有效期限"},

                // 英文到中文映射
                {"ID", "序号"},
                {"Name", "氏名"},
                {"Birthday", "生年月日"},
                {"Gender", "性别"},
                {"Address", "住所"},
                {"Phone", "电话番号"},

                // 其他可能的映射
                {"番号", "序号"},
                {"名前", "氏名"},
                {"電話", "电话番号"}
            };

            return commonMappings.ContainsKey(csvColumn) ? commonMappings[csvColumn] : null;
        }

        /// <summary>
        /// 转换CSV数据为Excel格式
        /// </summary>
        private List<Dictionary<string, object>> ConvertCsvDataToExcelFormat(
            List<Dictionary<string, object>> csvData,
            Dictionary<string, string> columnMapping,
            List<string> excelColumns)
        {
            var result = new List<Dictionary<string, object>>();

            foreach (var csvRow in csvData)
            {
                var excelRow = new Dictionary<string, object>();

                // 初始化所有Excel列为空值
                foreach (var excelColumn in excelColumns)
                {
                    excelRow[excelColumn] = "";
                }

                // 根据映射填充数据
                foreach (var csvColumn in csvRow.Keys)
                {
                    if (columnMapping.ContainsKey(csvColumn) && columnMapping[csvColumn] != null)
                    {
                        var excelColumn = columnMapping[csvColumn];
                        excelRow[excelColumn] = csvRow[csvColumn] ?? "";
                    }
                }

                result.Add(excelRow);
            }

            return result;
        }

        /// <summary>
        /// 查看详情按钮点击事件
        /// </summary>
        private void ViewDetailsButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var csvInfo = button?.Tag as CsvImportInfo;
            if (csvInfo == null) return;

            ShowImportDetails(csvInfo);
        }

        /// <summary>
        /// 再验证按钮点击事件
        /// </summary>
        private void RevalidateButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var csvInfo = button?.Tag as CsvImportInfo;
            if (csvInfo == null) return;

            var result = MessageBox.Show(
                $"ファイル「{csvInfo.FileName}」を再検証しますか？",
                "再検証確認",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                PerformRevalidation(csvInfo);
            }
        }

        /// <summary>
        /// 删除导入记录按钮点击事件
        /// </summary>
        private void DeleteImportButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var csvInfo = button?.Tag as CsvImportInfo;
            if (csvInfo == null) return;

            var result = MessageBox.Show(
                $"ファイル「{csvInfo.FileName}」の取込記録を削除しますか？\n\n注意：この操作は元に戻せません。",
                "削除確認",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                ImportedFiles.Remove(csvInfo);
                UpdateStatistics();
                MessageBox.Show("取込記録が削除されました。", "削除完了",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// 显示导入详情
        /// </summary>
        private void ShowImportDetails(CsvImportInfo csvInfo)
        {
            var detailWindow = new Window
            {
                Title = $"取込詳細 - {csvInfo.FileName}",
                Width = 700,
                Height = 600,
                WindowStartupLocation = WindowStartupLocation.CenterOwner,
                Owner = Window.GetWindow(this)
            };

            var scrollViewer = new ScrollViewer
            {
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                Padding = new Thickness(20)
            };

            var stackPanel = new StackPanel();

            // 基本信息
            AddDetailSection(stackPanel, "基本情報", new[]
            {
                ("No", csvInfo.ImportId.ToString()),
                ("ファイル名", csvInfo.FileName),
                ("ファイルパス", csvInfo.FilePath),
                ("ファイルサイズ", FormatFileSize(csvInfo.FileSize))
            });

            // 导入信息
            AddDetailSection(stackPanel, "取込情報", new[]
            {
                ("取込時間", csvInfo.ImportTime),
                ("処理時間", $"{csvInfo.ProcessingTime}ms"),
                ("取込状態", csvInfo.Status),
                ("検証状態", csvInfo.ValidationStatus)
            });

            // 统计信息
            AddDetailSection(stackPanel, "統計情報", new[]
            {
                ("総レコード数", csvInfo.RecordCount),
                ("成功件数", csvInfo.SuccessCount),
                ("エラー件数", csvInfo.ErrorCount),
                ("成功率", CalculateSuccessRate(csvInfo))
            });

            // 详细信息
            if (!string.IsNullOrEmpty(csvInfo.ImportDetails))
            {
                AddDetailSection(stackPanel, "詳細情報", new[]
                {
                    ("詳細", csvInfo.ImportDetails)
                });
            }

            // 错误信息
            if (csvInfo.ErrorMessages != null && csvInfo.ErrorMessages.Any())
            {
                var errorText = string.Join("\n", csvInfo.ErrorMessages.Take(10));
                if (csvInfo.ErrorMessages.Count > 10)
                {
                    errorText += $"\n... 他 {csvInfo.ErrorMessages.Count - 10} 件のエラー";
                }

                AddDetailSection(stackPanel, "エラー情報", new[]
                {
                    ("エラー内容", errorText)
                });
            }

            scrollViewer.Content = stackPanel;
            detailWindow.Content = scrollViewer;
            detailWindow.ShowDialog();
        }

        /// <summary>
        /// 执行再验证
        /// </summary>
        private async void PerformRevalidation(CsvImportInfo csvInfo)
        {
            try
            {
                csvInfo.ValidationStatus = "検証中";

                // 模拟验证过程
                await Task.Delay(1000);

                // 这里应该实现实际的验证逻辑
                if (File.Exists(csvInfo.FilePath))
                {
                    csvInfo.ValidationStatus = "検証済み";
                    MessageBox.Show("再検証が完了しました。", "検証完了",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    csvInfo.ValidationStatus = "検証エラー";
                    MessageBox.Show("ファイルが見つかりません。", "検証エラー",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                csvInfo.ValidationStatus = "検証エラー";
                MessageBox.Show($"検証中にエラーが発生しました：{ex.Message}", "検証エラー",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 添加详情区段
        /// </summary>
        private void AddDetailSection(StackPanel parent, string title, (string label, string value)[] items)
        {
            // 标题
            var titleBlock = new TextBlock
            {
                Text = title,
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 15, 0, 10),
                Foreground = Brushes.DarkSlateGray
            };
            parent.Children.Add(titleBlock);

            // 内容网格
            var grid = new Grid
            {
                Margin = new Thickness(10, 0, 0, 0)
            };

            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(120) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            for (int i = 0; i < items.Length; i++)
            {
                grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

                var labelBlock = new TextBlock
                {
                    Text = items[i].label + ":",
                    FontWeight = FontWeights.Medium,
                    Margin = new Thickness(0, 2, 10, 2),
                    VerticalAlignment = VerticalAlignment.Top
                };
                Grid.SetRow(labelBlock, i);
                Grid.SetColumn(labelBlock, 0);
                grid.Children.Add(labelBlock);

                var valueBlock = new TextBlock
                {
                    Text = items[i].value ?? "-",
                    Margin = new Thickness(0, 2, 0, 2),
                    TextWrapping = TextWrapping.Wrap,
                    VerticalAlignment = VerticalAlignment.Top
                };
                Grid.SetRow(valueBlock, i);
                Grid.SetColumn(valueBlock, 1);
                grid.Children.Add(valueBlock);
            }

            parent.Children.Add(grid);
        }

        /// <summary>
        /// 格式化文件大小
        /// </summary>
        private string FormatFileSize(long bytes)
        {
            if (bytes == 0) return "0 B";

            string[] sizes = { "B", "KB", "MB", "GB" };
            int order = 0;
            double size = bytes;

            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size /= 1024;
            }

            return $"{size:F2} {sizes[order]}";
        }

        /// <summary>
        /// 计算成功率
        /// </summary>
        private string CalculateSuccessRate(CsvImportInfo csvInfo)
        {
            if (int.TryParse(csvInfo.RecordCount, out int total) && total > 0 &&
                int.TryParse(csvInfo.SuccessCount, out int success))
            {
                double rate = (double)success / total * 100;
                return $"{rate:F1}%";
            }
            return "0%";
        }

        /// <summary>
        /// 更新统计信息
        /// </summary>
        private void UpdateStatistics()
        {
            TotalFilesText.Text = ImportedFiles.Count.ToString();

            var totalRecords = ImportedFiles.Sum(f =>
            {
                if (int.TryParse(f.RecordCount.Replace(",", ""), out int count))
                    return count;
                return 0;
            });

            TotalRecordsText.Text = totalRecords.ToString("N0");

            var lastFile = ImportedFiles.OrderByDescending(f => f.ImportTime).FirstOrDefault();
            LastUpdateText.Text = lastFile?.ImportTime ?? "-";
        }

        /// <summary>
        /// 更新UI状态
        /// </summary>
        private void UpdateUI()
        {
            SelectCsvButton.IsEnabled = !_isImporting;
            DataMappingButton.IsEnabled = !_isImporting && _currentCsvInfo != null;
            ImportExecuteButton.IsEnabled = !_isImporting && _currentCsvInfo != null && _currentCsvInfo.Status == "検証成功";
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// CSV导入结果类
    /// </summary>
    public class CsvImportResult
    {
        public bool Success { get; set; }
        public int ImportedRecords { get; set; }
        public int SuccessCount { get; set; }
        public int SkippedCount { get; set; }
        public int ErrorCount { get; set; }
        public double ProcessingTime { get; set; }
        public string ErrorMessage { get; set; }
        public List<string> ErrorMessages { get; set; } = new List<string>();
        public Dictionary<string, string> ColumnMappings { get; set; } = new Dictionary<string, string>();
    }

    /// <summary>
    /// CSV导入信息类
    /// </summary>
    public class CsvImportInfo : INotifyPropertyChanged
    {
        private string _status;
        private string _validationStatus;
        private static int _nextId = 1;

        public CsvImportInfo()
        {
            ImportId = _nextId++;
            ValidationStatus = "未検証";
            SuccessCount = "0";
            ErrorCount = "0";
            ErrorMessages = new List<string>();
        }

        /// <summary>
        /// 导入序号
        /// </summary>
        public int ImportId { get; set; }

        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// 文件路径
        /// </summary>
        public string FilePath { get; set; }

        /// <summary>
        /// 导入时间
        /// </summary>
        public string ImportTime { get; set; }

        /// <summary>
        /// 总记录数
        /// </summary>
        public string RecordCount { get; set; }

        /// <summary>
        /// 成功导入条数
        /// </summary>
        public string SuccessCount { get; set; }

        /// <summary>
        /// 错误条数
        /// </summary>
        public string ErrorCount { get; set; }

        /// <summary>
        /// 导入状态
        /// </summary>
        public string Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged(nameof(Status));
            }
        }

        /// <summary>
        /// 验证状态
        /// </summary>
        public string ValidationStatus
        {
            get => _validationStatus;
            set
            {
                _validationStatus = value;
                OnPropertyChanged(nameof(ValidationStatus));
            }
        }

        /// <summary>
        /// 错误消息列表
        /// </summary>
        public List<string> ErrorMessages { get; set; }

        /// <summary>
        /// 导入详细信息
        /// </summary>
        public string ImportDetails { get; set; }

        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 处理耗时（毫秒）
        /// </summary>
        public long ProcessingTime { get; set; }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
