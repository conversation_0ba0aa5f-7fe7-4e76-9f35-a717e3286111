<UserControl x:Class="NAVI.NationalDataControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:controls="clr-namespace:NAVI.Controls"
             mc:Ignorable="d"
             d:DesignHeight="500" d:DesignWidth="900">
    
    <UserControl.Resources>
        <!-- 现代化按钮样式 -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF2986A8"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                               CornerRadius="4" 
                               Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#FF1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#FF1565C0"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- 次要按钮样式 -->
        <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#FF757575"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#FF616161"/>
                </Trigger>
            </Style.Triggers>
        </Style>
        
        <!-- 危险按钮样式 -->
        <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#FFF44336"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#FFD32F2F"/>
                </Trigger>
            </Style.Triggers>
        </Style>
        
        <!-- 现代化文本框样式 -->
        <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#FFCCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}" 
                               BorderBrush="{TemplateBinding BorderBrush}" 
                               BorderThickness="{TemplateBinding BorderThickness}" 
                               CornerRadius="4">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                         Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#FF2986A8"/>
                                <Setter Property="BorderThickness" Value="2"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- DataGrid样式 -->
        <Style x:Key="ModernDataGridStyle" TargetType="DataGrid">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#FFE0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HorizontalGridLinesBrush" Value="#FFF0F0F0"/>
            <Setter Property="RowBackground" Value="White"/>
            <Setter Property="AlternatingRowBackground" Value="#FFF8F9FA"/>
            <Setter Property="HeadersVisibility" Value="Column"/>
            <Setter Property="CanUserResizeRows" Value="False"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="AutoGenerateColumns" Value="False"/>
            <Setter Property="SelectionMode" Value="Single"/>
            <Setter Property="SelectionUnit" Value="FullRow"/>
        </Style>
        
        <!-- DataGrid列标题样式 -->
        <Style x:Key="ModernDataGridColumnHeaderStyle" TargetType="DataGridColumnHeader">
            <Setter Property="Background" Value="#FF37474F"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="BorderThickness" Value="0,0,1,0"/>
            <Setter Property="BorderBrush" Value="#FF546E7A"/>
        </Style>
    </UserControl.Resources>

    <Grid Background="#FFF8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 顶部内容区 -->
        <Border Grid.Row="0" 
               Background="White" 
               BorderBrush="#FFE0E0E0" 
               BorderThickness="0,0,0,1" 
               Padding="24,20">
            <StackPanel>
                <!-- 页面标题 -->
                <TextBlock Text="国保連データ" 
                          FontSize="24" 
                          FontWeight="Medium" 
                          Foreground="#FF212121" 
                          Margin="0,0,0,20"/>
                
                <!-- 操作按钮区域 - 全部居左显示 -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                    <!-- 新增记录按钮 -->
                    <Button Content="新增记录" 
                           Style="{StaticResource ModernButtonStyle}" 
                           Margin="0,0,12,0"
                           Click="AddButton_Click">
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border Background="{TemplateBinding Background}" 
                                       CornerRadius="4" 
                                       Padding="{TemplateBinding Padding}">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Plus" 
                                                               Width="16" Height="16" 
                                                               VerticalAlignment="Center" 
                                                               Margin="0,0,8,0"/>
                                        <ContentPresenter VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#FF1976D2"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Button.Template>
                    </Button>
                    
                    <!-- 搜索框 -->
                    <TextBox x:Name="SearchTextBox" 
                            Width="280" 
                            Height="36" 
                            Style="{StaticResource ModernTextBoxStyle}" 
                            VerticalAlignment="Center" 
                            Margin="0,0,12,0"
                            Text="输入保险者番号・被保险者番号・氏名・其他关键字">
                        <TextBox.Template>
                            <ControlTemplate TargetType="TextBox">
                                <Border Background="{TemplateBinding Background}" 
                                       BorderBrush="{TemplateBinding BorderBrush}" 
                                       BorderThickness="{TemplateBinding BorderThickness}" 
                                       CornerRadius="4">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <materialDesign:PackIcon Grid.Column="0" 
                                                               Kind="Magnify" 
                                                               Width="16" Height="16" 
                                                               Margin="12,0,8,0" 
                                                               VerticalAlignment="Center" 
                                                               Foreground="#FF757575"/>
                                        <ScrollViewer x:Name="PART_ContentHost" 
                                                     Grid.Column="1" 
                                                     Margin="0,8,12,8"/>
                                    </Grid>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsFocused" Value="True">
                                        <Setter Property="BorderBrush" Value="#FF2986A8"/>
                                        <Setter Property="BorderThickness" Value="2"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </TextBox.Template>
                    </TextBox>
                    
                    <!-- 搜索按钮 -->
                    <Button Content="搜索" 
                           Style="{StaticResource ModernButtonStyle}" 
                           Width="80" Height="36" 
                           Margin="0,0,12,0"
                           Click="SearchButton_Click"/>
                    
                    <!-- 导出按钮 -->
                    <Button Content="导出" 
                           Style="{StaticResource SecondaryButtonStyle}" 
                           Width="70" Height="36" 
                           Margin="0,0,12,0"
                           Click="ExportButton_Click"/>
                    
                    <!-- 打印按钮 -->
                    <Button Content="打印" 
                           Style="{StaticResource SecondaryButtonStyle}" 
                           Width="70" Height="36" 
                           Margin="0,0,12,0"
                           Click="PrintButton_Click"/>
                    
                    <!-- 刷新按钮 -->
                    <Button Content="刷新" 
                           Style="{StaticResource SecondaryButtonStyle}" 
                           Width="70" Height="36"
                           Click="RefreshButton_Click"/>
                </StackPanel>
            </StackPanel>
        </Border>
        
        <!-- 数据表格区域 -->
        <Border Grid.Row="1" 
               Background="White" 
               Margin="24,16,24,0" 
               CornerRadius="8,8,0,0" 
               BorderBrush="#FFE0E0E0" 
               BorderThickness="1,1,1,0">
            <DataGrid x:Name="NationalDataGrid"
                     Style="{StaticResource ModernDataGridStyle}"
                     ColumnHeaderStyle="{StaticResource ModernDataGridColumnHeaderStyle}"
                     Margin="0">
                <DataGrid.Columns>
                    <DataGridTemplateColumn Header="操作" Width="Auto" MinWidth="100" CanUserSort="False">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal"
                                           HorizontalAlignment="Center"
                                           Margin="4">
                                    <Button Content="编辑"
                                           Width="75" Height="28"
                                           FontSize="11"
                                           Style="{StaticResource ModernButtonStyle}"
                                           Margin="0,0,4,0"
                                           Click="EditButton_Click"/>
                                    <Button Content="删除"
                                           Width="75" Height="28"
                                           FontSize="11"
                                           Style="{StaticResource DangerButtonStyle}"
                                           Click="DeleteButton_Click"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <!-- 其他列将动态生成 -->
                </DataGrid.Columns>
            </DataGrid>
        </Border>
        
        <!-- 分页控件 -->
        <controls:PaginationControl x:Name="PaginationControl" 
                                   Grid.Row="2" 
                                   Margin="24,0,24,24"
                                   PageChanged="PaginationControl_PageChanged"
                                   PageSizeChanged="PaginationControl_PageSizeChanged"/>
    </Grid>
</UserControl>
