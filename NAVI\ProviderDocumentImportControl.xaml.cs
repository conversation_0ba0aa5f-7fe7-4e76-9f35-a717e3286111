using Microsoft.Win32;
using NAVI.Services;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Imaging;

namespace NAVI
{
    /// <summary>
    /// 事业者纸资料导入控件
    /// </summary>
    public partial class ProviderDocumentImportControl : UserControl, INotifyPropertyChanged
    {
        private ObservableCollection<ProcessedImageInfo> _processedImages;
        private string _currentImagePath;
        private bool _isProcessing = false;

        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// 处理过的图像列表
        /// </summary>
        public ObservableCollection<ProcessedImageInfo> ProcessedImages
        {
            get => _processedImages;
            set
            {
                _processedImages = value;
                OnPropertyChanged(nameof(ProcessedImages));
            }
        }

        public ProviderDocumentImportControl()
        {
            InitializeComponent();
            InitializeData();
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            ProcessedImages = new ObservableCollection<ProcessedImageInfo>();
            DataContext = this;

            // 添加示例数据
            ProcessedImages.Add(new ProcessedImageInfo
            {
                FileName = "sample_document_001.jpg",
                ProcessTime = "2025-05-15 14:30",
                RecognitionStatus = "認識成功",
                DataStatus = "保存済み"
            });
        }

        /// <summary>
        /// 选择图像文件按钮点击事件
        /// </summary>
        private void SelectImageButton_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "选择图像文件",
                Filter = "图像文件 (*.jpg;*.jpeg;*.png;*.bmp;*.tiff)|*.jpg;*.jpeg;*.png;*.bmp;*.tiff|所有文件 (*.*)|*.*",
                CheckFileExists = true
            };

            if (openFileDialog.ShowDialog() == true)
            {
                LoadImageFile(openFileDialog.FileName);
            }
        }

        /// <summary>
        /// 加载图像文件
        /// </summary>
        private void LoadImageFile(string filePath)
        {
            try
            {
                _currentImagePath = filePath;

                // 加载并显示图像
                var bitmap = new BitmapImage();
                bitmap.BeginInit();
                bitmap.UriSource = new Uri(filePath);
                bitmap.CacheOption = BitmapCacheOption.OnLoad;
                bitmap.EndInit();

                PreviewImage.Source = bitmap;
                PreviewImage.Visibility = Visibility.Visible;
                EmptyImageState.Visibility = Visibility.Collapsed;

                // 启用OCR处理按钮
                OcrProcessButton.IsEnabled = true;

                MessageBox.Show($"图像文件加载成功：{Path.GetFileName(filePath)}", "成功",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载图像文件失败：{ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// OCR处理按钮点击事件
        /// </summary>
        private async void OcrProcessButton_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(_currentImagePath))
            {
                MessageBox.Show("请先选择要处理的图像文件", "提示",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            await ProcessOcrAsync();
        }

        /// <summary>
        /// 执行OCR处理
        /// </summary>
        private async Task ProcessOcrAsync()
        {
            try
            {
                _isProcessing = true;
                UpdateUI();

                // 显示处理进度
                ProcessingOverlay.Visibility = Visibility.Visible;

                //JObject json = await Utils.ClaudeOcrHelper.ProcessImageAsync(_currentImagePath);
                //int result = ImportFromOcrImageAsync(_currentImagePath).Result;
                // 在需要的地方调用（比如OCR识别完成后）
                int importedCount = 0;
                //var providerImportControl = new ProviderExcelImportControl();
                try
                {
                    importedCount = await ImportFromOcrImageAsync(_currentImagePath);
                    //MessageBox.Show($"OCR数据导入成功，共导入 {importedCount} 条记录", "导入成功");
                }
                catch (Exception ex)
                {
                    //MessageBox.Show($"OCR数据导入失败：{ex.Message}", "导入失败");
                }

                // 模拟OCR处理时间
                await Task.Delay(3000);

                // 创建处理结果记录
                var processedImage = new ProcessedImageInfo
                {
                    FileName = Path.GetFileName(_currentImagePath),
                    ProcessTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm"),
                    ImportCount = importedCount.ToString(),
                    RecognitionStatus = "認識成功",
                    DataStatus = "保存済み"
                };

                ProcessedImages.Add(processedImage);

                MessageBox.Show("OCR处理完成！文字识别成功。", "处理完成",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"OCR处理失败：{ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);

                // 添加失败记录
                var failedImage = new ProcessedImageInfo
                {
                    FileName = Path.GetFileName(_currentImagePath),
                    ProcessTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm"),
                    ImportCount = "0",
                    RecognitionStatus = "認識失敗",
                    DataStatus = "エラー"
                };

                ProcessedImages.Add(failedImage);
            }
            finally
            {
                _isProcessing = false;
                ProcessingOverlay.Visibility = Visibility.Collapsed;
                UpdateUI();
            }
        }

        /// <summary>
        /// 清除按钮点击事件
        /// </summary>
        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("确定要清除当前内容吗？", "确认清除",
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                ClearCurrentContent();
            }
        }

        /// <summary>
        /// 清除当前内容
        /// </summary>
        private void ClearCurrentContent()
        {
            _currentImagePath = null;
            PreviewImage.Source = null;
            PreviewImage.Visibility = Visibility.Collapsed;
            EmptyImageState.Visibility = Visibility.Visible;
            OcrProcessButton.IsEnabled = false;
        }

        /// <summary>
        /// 处理结果列表选择变化事件
        /// </summary>
        private void ProcessedImagesGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // 这里可以添加选择变化的处理逻辑
        }

        /// <summary>
        /// 查看详情按钮点击事件
        /// </summary>
        private void ViewDetailsButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is ProcessedImageInfo imageInfo)
            {
                // 这里应该打开详情查看窗口
                MessageBox.Show($"查看详情功能待实现\n文件：{imageInfo.FileName}\n处理时间：{imageInfo.ProcessTime}",
                    "详情查看", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// 更新UI状态
        /// </summary>
        private void UpdateUI()
        {
            SelectImageButton.IsEnabled = !_isProcessing;
            OcrProcessButton.IsEnabled = !_isProcessing && !string.IsNullOrEmpty(_currentImagePath);
            ClearButton.IsEnabled = !_isProcessing;
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// 将OCR识别的JSON结果保存到Excel
        /// </summary>
        /// <param name="imagePath">图片路径</param>
        /// <returns>导入的记录数</returns>
        public async Task<int> ImportFromOcrImageAsync(string imagePath)
        {
            try
            {
                // 1. 使用OCR识别图片
                //JObject json = await Utils.ClaudeOcrHelper.ProcessImageAsync(imagePath);
                string jsonstring = "{"
    + "\"title\": \"都加算明細書\","
    + "\"date\": {"
        + "\"era\": \"令和\","
        + "\"year\": \"7\","
        + "\"month\": \"2\""
    + "},"
    + "\"recipient_info\": {"
        + "\"certificate_number\": \"1234567890\","
        + "\"support_decision_disabled_person_name\": \"鈴木太郎\","
        + "\"support_decision_disabled_child_name\": \"鈴木次郎\","
        + "\"disability_support_classification\": \"5\","
        + "\"start_date\": \"2025/2/1\","
        + "\"end_date\": \"2025/2/28\","
        + "\"usage_days\": \"28\""
    + "},"
    + "\"business_office_info\": {"
        + "\"office_number\": \"**********\","
        + "\"business_name\": \"安心ケア乃舎\","
        + "\"regional_classification\": \"1級地\","
        + "\"former_physical_care_facility_classification\": \"該当\","
        + "\"psychiatric_medical_cooperation_system_addition\": \"東京可\""
    + "},"
    + "\"basic_compensation\": ["
        + "{"
            + "\"service_code\": \"241112\","
            + "\"service_content\": \"福祉短期入所15\","
            + "\"calculation_unit_price\": \"11,756\","
            + "\"usage_days\": \"28\","
            + "\"monthly_calculation_amount\": \"329,168\","
            + "\"remarks\": \"土対応\""
        + "},"
        + "{"
            + "\"service_code\": \"241156\","
            + "\"service_content\": \"福祉短期入所15・大規模減算\","
            + "\"calculation_unit_price\": \"11,756\","
            + "\"usage_days\": \"12\","
            + "\"monthly_calculation_amount\": \"141,072\","
            + "\"remarks\": \"対応済み\""
        + "}"
    + "],"
    + "\"basic_subtotal\": \"470,240\","
    + "\"addition_items\": ["
        + "{"
            + "\"service_code\": \"246080\","
            + "\"content\": \"短期医療連携体制加算IV1\","
            + "\"calculation_unit_price\": \"698\","
            + "\"usage_days\": null,"
            + "\"monthly_calculation_amount\": null"
        + "},"
        + "{"
            + "\"service_code\": \"246081\","
            + "\"content\": \"短期医療連携体制加算IV2\","
            + "\"calculation_unit_price\": \"436\","
            + "\"usage_days\": null,"
            + "\"monthly_calculation_amount\": null"
        + "},"
        + "{"
            + "\"service_code\": \"246082\","
            + "\"content\": \"短期医療連携体制加算IV3\","
            + "\"calculation_unit_price\": \"349\","
            + "\"usage_days\": null,"
            + "\"monthly_calculation_amount\": null"
        + "},"
        + "{"
            + "\"service_code\": \"246992\","
            + "\"content\": \"短期医療連携体制加算VII\","
            + "\"calculation_unit_price\": \"別途より\","
            + "\"usage_days\": null,"
            + "\"monthly_calculation_amount\": \"4,190\""
        + "},"
        + "{"
            + "\"service_code\": \"246068\","
            + "\"content\": \"短期医療連携体制加算VIII\","
            + "\"calculation_unit_price\": \"790\","
            + "\"usage_days\": null,"
            + "\"monthly_calculation_amount\": null"
        + "}"
    + "],"
    + "\"psychiatric_medical_cooperation_addition\": {"
        + "\"unit_price\": \"3,423\","
        + "\"usage_days\": \"40\","
        + "\"amount\": \"136,920\""
    + "},"
    + "\"addition_subtotal\": \"141,110\","
    + "\"monthly_total_request_amount\": \"611,350\","
    + "\"currency\": \"円\""
+ "}";

                JObject json = JObject.Parse(jsonstring);

                var importService = new ProviderExcelImportService();
                // 2. 将JSON转换为Excel数据格式
                var importData = importService.ConvertJsonToImportData(json);

                // 3. 保存到Excel
                var result = await importService.ImportOcrDataToExcelAsync(importData);

                return result.ImportedRecordCount;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"OCR导入失败: {ex.Message}");
                throw;
            }
        }
    }

    /// <summary>
    /// 处理过的图像信息类
    /// </summary>
    public class ProcessedImageInfo : INotifyPropertyChanged
    {
        private string _recognitionStatus;
        private string _dataStatus;

        public string FileName { get; set; }
        public string ProcessTime { get; set; }

        public string ImportCount { get; set; } = "0";

        public string RecognitionStatus
        {
            get => _recognitionStatus;
            set
            {
                _recognitionStatus = value;
                OnPropertyChanged(nameof(RecognitionStatus));
            }
        }

        public string DataStatus
        {
            get => _dataStatus;
            set
            {
                _dataStatus = value;
                OnPropertyChanged(nameof(DataStatus));
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
