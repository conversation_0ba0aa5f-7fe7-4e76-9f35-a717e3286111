<UserControl x:Class="NAVI.ProviderExcelImportControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000">

    <UserControl.Resources>
        <!-- 现代化按钮样式 -->
        <Style x:Key="MaterialDesignRaisedButton" TargetType="Button">
            <Setter Property="Background" Value="#FF2986A8"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                       CornerRadius="4" 
                       Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                    VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#FF1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#FF1565C0"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <!-- 按钮样式 -->
        <Style x:Key="ImportButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Height" Value="40"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
        </Style>

        <!-- 文件列表样式 -->
        <Style x:Key="FileListStyle" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
            <Setter Property="AutoGenerateColumns" Value="False"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="CanUserDeleteRows" Value="False"/>
            <Setter Property="IsReadOnly" Value="True"/>
            <Setter Property="SelectionMode" Value="Extended"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HeadersVisibility" Value="Column"/>
        </Style>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" 
                   Text="事業者Excel取込" 
                   FontSize="24" 
                   FontWeight="Bold" 
                   Foreground="#FF2196F3"
                   Margin="0,0,0,20"/>

        <!-- 操作按钮区域 -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,20">
            <Button Name="SelectFilesButton"
                    Content="選択文件"
                    Style="{StaticResource ImportButtonStyle}"
                    Background="#FF2196F3"
                    Click="SelectFilesButton_Click">
                <Button.ToolTip>
                    <ToolTip Content="选择要导入的Excel文件（支持多选）"/>
                </Button.ToolTip>
            </Button>

            <Button Name="ImportButton"
                    Content="一括ファイル取込実行"
                    Style="{StaticResource ImportButtonStyle}"
                    Background="#FF4CAF50"
                    IsEnabled="False"
                    Click="ImportButton_Click">
                <Button.ToolTip>
                    <ToolTip Content="执行批量导入操作"/>
                </Button.ToolTip>
            </Button>

            <Button Name="CancelButton"
                    Content="キャンセル"
                    Style="{StaticResource ImportButtonStyle}"
                    Background="#FFF44336"
                    Click="CancelButton_Click">
                <Button.ToolTip>
                    <ToolTip Content="取消当前操作"/>
                </Button.ToolTip>
            </Button>

            <Button Name="CreateTestFileButton"
                    Content="テストファイル作成"
                    Style="{StaticResource ImportButtonStyle}"
                    Background="#FF9C27B0"
                    Click="CreateTestFileButton_Click">
                <Button.ToolTip>
                    <ToolTip Content="创建测试用的Excel文件"/>
                </Button.ToolTip>
            </Button>
        </StackPanel>

        <!-- 说明文字 -->
        <TextBlock Grid.Row="2" 
                   Text="事業者のExcelファイルを一括取込します（最大200ファイル）。"
                   FontSize="14"
                   Foreground="#FF666666"
                   Margin="0,0,0,15"/>

        <!-- 文件列表 -->
        <Border Grid.Row="3" 
                BorderBrush="#FFE0E0E0" 
                BorderThickness="1" 
                CornerRadius="4"
                Background="White">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 列表标题 -->
                <Border Grid.Row="0" 
                        Background="#FFF5F5F5" 
                        BorderBrush="#FFE0E0E0" 
                        BorderThickness="0,0,0,1"
                        Padding="10">
                    <TextBlock Text="選択済みファイル一覧:" 
                               FontWeight="Medium" 
                               FontSize="14"/>
                </Border>

                <!-- 文件数据网格 -->
                <DataGrid Grid.Row="1" 
                          Name="FileListGrid"
                          Style="{StaticResource FileListStyle}"
                          ItemsSource="{Binding SelectedFiles}"
                          Margin="5">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="ファイル名" 
                                          Binding="{Binding FileName}" 
                                          Width="300"/>
                        <DataGridTextColumn Header="ファイルサイズ" 
                                          Binding="{Binding FileSize}" 
                                          Width="100"/>
                        <DataGridTextColumn Header="最終更新日時" 
                                          Binding="{Binding LastModified}" 
                                          Width="150"/>
                        <DataGridTextColumn Header="状態" 
                                          Binding="{Binding Status}" 
                                          Width="100"/>
                        <DataGridTextColumn Header="レコード数" 
                                          Binding="{Binding RecordCount}" 
                                          Width="100"/>
                        <DataGridTemplateColumn Header="操作" Width="80">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Content="削除" 
                                            Background="#FFF44336"
                                            Foreground="White"
                                            FontSize="12"
                                            Height="25"
                                            Click="RemoveFileButton_Click"
                                            Tag="{Binding}"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- 空状态提示 -->
                <TextBlock Grid.Row="1"
                           Name="EmptyStateText"
                           Text="画像ファイルを選択してください。"
                           FontSize="16"
                           Foreground="#FF999999"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           Visibility="Visible"/>
            </Grid>
        </Border>

        <!-- 进度和状态信息 -->
        <Grid Grid.Row="4" Margin="0,15,0,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 进度条 -->
            <ProgressBar Grid.Row="0" 
                         Name="ImportProgressBar"
                         Height="8"
                         Margin="0,0,0,10"
                         Visibility="Collapsed"/>

            <!-- 状态信息 -->
            <StackPanel Grid.Row="1" Orientation="Horizontal">
                <TextBlock Text="状態: " FontWeight="Medium"/>
                <TextBlock Name="StatusText" Text="ファイル選択待ち..." Foreground="#FF666666"/>
                <TextBlock Text=" | 選択ファイル数: " FontWeight="Medium" Margin="20,0,0,0"/>
                <TextBlock Name="FileCountText" Text="0" Foreground="#FF2196F3"/>
                <TextBlock Text=" | 処理済み: " FontWeight="Medium" Margin="20,0,0,0"/>
                <TextBlock Name="ProcessedCountText" Text="0" Foreground="#FF4CAF50"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
