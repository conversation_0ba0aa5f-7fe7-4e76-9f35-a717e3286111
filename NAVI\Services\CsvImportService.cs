using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NAVI.Services.DAL;

namespace NAVI.Services
{
    /// <summary>
    /// CSV导入服务
    /// </summary>
    public class CsvImportService
    {
        private readonly DatabaseManager _databaseManager;

        public CsvImportService()
        {
            _databaseManager = new DatabaseManager();
        }

        /// <summary>
        /// 导入用户CSV数据
        /// </summary>
        /// <param name="csvFilePath">CSV文件路径</param>
        /// <param name="encoding">文件编码，默认为UTF-8</param>
        /// <returns>导入结果</returns>
        public async Task<CsvImportResult> ImportUserCsvAsync(string csvFilePath, Encoding encoding = null)
        {
            var result = new CsvImportResult
            {
                FileName = Path.GetFileName(csvFilePath),
                ImportTime = DateTime.Now,
                IsSuccess = false
            };

            try
            {
                if (encoding == null)
                    encoding = Encoding.UTF8;

                // 读取CSV文件
                var csvData = ReadCsvFile(csvFilePath, encoding);
                if (csvData.Rows.Count == 0)
                {
                    result.ErrorMessage = "CSV文件为空或格式不正确";
                    return result;
                }

                // 映射CSV列到用户字段
                var mappedUsers = MapCsvToUsers(csvData);
                if (mappedUsers.Count == 0)
                {
                    result.ErrorMessage = "没有找到有效的用户数据";
                    return result;
                }

                // 验证数据
                var validationResult = await ValidateUsers(mappedUsers);
                if (!validationResult.IsValid)
                {
                    result.ErrorMessage = validationResult.ErrorMessage;
                    result.ValidationErrors = validationResult.Warnings;
                    return result;
                }

                // 导入到数据库
                var importedCount = await ImportUsersToDatabase(mappedUsers);

                result.IsSuccess = true;
                result.TotalRecords = csvData.Rows.Count;
                result.ImportedRecords = importedCount;
                result.SuccessMessage = $"成功导入 {importedCount} 条用户记录";

                return result;
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"导入失败：{ex.Message}";
                return result;
            }
        }

        /// <summary>
        /// 读取CSV文件
        /// </summary>
        private DataTable ReadCsvFile(string filePath, Encoding encoding)
        {
            var dataTable = new DataTable();
            var lines = File.ReadAllLines(filePath, encoding);

            if (lines.Length == 0)
                return dataTable;

            // 解析表头
            var headers = ParseCsvLine(lines[0]);
            foreach (var header in headers)
            {
                dataTable.Columns.Add(header.Trim());
            }

            // 解析数据行
            for (int i = 1; i < lines.Length; i++)
            {
                var values = ParseCsvLine(lines[i]);
                if (values.Length > 0 && !string.IsNullOrWhiteSpace(string.Join("", values)))
                {
                    var row = dataTable.NewRow();
                    for (int j = 0; j < Math.Min(values.Length, dataTable.Columns.Count); j++)
                    {
                        row[j] = values[j]?.Trim() ?? "";
                    }
                    dataTable.Rows.Add(row);
                }
            }

            return dataTable;
        }

        /// <summary>
        /// 解析CSV行
        /// </summary>
        private string[] ParseCsvLine(string line)
        {
            var result = new List<string>();
            var current = new StringBuilder();
            bool inQuotes = false;

            for (int i = 0; i < line.Length; i++)
            {
                char c = line[i];

                if (c == '"')
                {
                    inQuotes = !inQuotes;
                }
                else if (c == ',' && !inQuotes)
                {
                    result.Add(current.ToString());
                    current.Clear();
                }
                else
                {
                    current.Append(c);
                }
            }

            result.Add(current.ToString());
            return result.ToArray();
        }

        /// <summary>
        /// 映射CSV数据到用户对象
        /// </summary>
        private List<User> MapCsvToUsers(DataTable csvData)
        {
            var users = new List<User>();
            var columnMapping = CreateColumnMapping(csvData.Columns);

            foreach (DataRow row in csvData.Rows)
            {
                var user = new User();
                bool hasValidData = false;

                // 映射各个字段
                if (columnMapping.ContainsKey("職員番号"))
                {
                    user.職員番号 = row[columnMapping["職員番号"]]?.ToString()?.Trim() ?? "";
                    if (!string.IsNullOrEmpty(user.職員番号)) hasValidData = true;
                }

                if (columnMapping.ContainsKey("部署名"))
                    user.部署名 = row[columnMapping["部署名"]]?.ToString()?.Trim() ?? "";

                if (columnMapping.ContainsKey("役職"))
                    user.役職 = row[columnMapping["役職"]]?.ToString()?.Trim() ?? "";

                if (columnMapping.ContainsKey("氏名"))
                {
                    user.氏名 = row[columnMapping["氏名"]]?.ToString()?.Trim() ?? "";
                    if (!string.IsNullOrEmpty(user.氏名)) hasValidData = true;
                }

                if (columnMapping.ContainsKey("ID番号"))
                {
                    user.ID番号 = row[columnMapping["ID番号"]]?.ToString()?.Trim() ?? "";
                    if (!string.IsNullOrEmpty(user.ID番号)) hasValidData = true;
                }

                if (columnMapping.ContainsKey("パスワード"))
                    user.パスワード = row[columnMapping["パスワード"]]?.ToString()?.Trim() ?? "123456"; // 默认密码

                // 设置时间戳
                var now = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                user.作成日時 = now;
                user.更新日時 = now;

                // 只有包含必要字段的记录才添加
                if (hasValidData && !string.IsNullOrEmpty(user.職員番号) && !string.IsNullOrEmpty(user.氏名) && !string.IsNullOrEmpty(user.ID番号))
                {
                    users.Add(user);
                }
            }

            return users;
        }

        /// <summary>
        /// 创建列映射
        /// </summary>
        private Dictionary<string, int> CreateColumnMapping(DataColumnCollection columns)
        {
            var mapping = new Dictionary<string, int>();
            var fieldMappings = new Dictionary<string, string[]>
            {
                { "職員番号", new[] { "職員番号", "员工编号", "员工号", "职员编号", "职员号", "EmployeeNumber", "EmpNo" } },
                { "部署名", new[] { "部署名", "部门", "部门名称", "Department", "Dept" } },
                { "役職", new[] { "役職", "职位", "职务", "岗位", "Position", "Title" } },
                { "氏名", new[] { "氏名", "姓名", "名前", "Name", "FullName" } },
                { "ID番号", new[] { "ID番号", "ID号", "用户ID", "登录ID", "UserID", "LoginID", "ID" } },
                { "パスワード", new[] { "パスワード", "密码", "Password", "Pass" } }
            };

            for (int i = 0; i < columns.Count; i++)
            {
                var columnName = columns[i].ColumnName.Trim();
                
                foreach (var field in fieldMappings)
                {
                    if (field.Value.Any(alias => string.Equals(alias, columnName, StringComparison.OrdinalIgnoreCase)))
                    {
                        mapping[field.Key] = i;
                        break;
                    }
                }
            }

            return mapping;
        }

        /// <summary>
        /// 验证用户数据
        /// </summary>
        private async Task<ValidationResult> ValidateUsers(List<User> users)
        {
            var result = new ValidationResult { IsValid = true };
            var errors = new List<string>();

            // 检查重复的职员番号和ID番号
            var duplicateEmployeeNumbers = users.GroupBy(u => u.職員番号)
                .Where(g => g.Count() > 1)
                .Select(g => g.Key);

            var duplicateIdNumbers = users.GroupBy(u => u.ID番号)
                .Where(g => g.Count() > 1)
                .Select(g => g.Key);

            if (duplicateEmployeeNumbers.Any())
            {
                errors.Add($"CSV中存在重复的職員番号: {string.Join(", ", duplicateEmployeeNumbers)}");
            }

            if (duplicateIdNumbers.Any())
            {
                errors.Add($"CSV中存在重复的ID番号: {string.Join(", ", duplicateIdNumbers)}");
            }

            // 检查数据库中是否已存在
            foreach (var user in users)
            {
                if (await _databaseManager.Users.EmployeeNumberExistsAsync(user.職員番号))
                {
                    errors.Add($"職員番号 {user.職員番号} 已存在于数据库中");
                }

                if (await _databaseManager.Users.IdNumberExistsAsync(user.ID番号))
                {
                    errors.Add($"ID番号 {user.ID番号} 已存在于数据库中");
                }
            }

            if (errors.Any())
            {
                result.IsValid = false;
                result.ErrorMessage = "数据验证失败";
                result.Warnings = errors;
            }

            return result;
        }

        /// <summary>
        /// 导入用户到数据库
        /// </summary>
        private async Task<int> ImportUsersToDatabase(List<User> users)
        {
            int importedCount = 0;

            foreach (var user in users)
            {
                try
                {
                    await _databaseManager.Users.CreateUserAsync(user);
                    importedCount++;
                }
                catch (Exception ex)
                {
                    // 记录错误但继续处理其他记录
                    System.Diagnostics.Debug.WriteLine($"导入用户失败 {user.職員番号}: {ex.Message}");
                }
            }

            return importedCount;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _databaseManager?.Dispose();
        }
    }

    /// <summary>
    /// CSV导入结果
    /// </summary>
    public class CsvImportResult
    {
        public string FileName { get; set; }
        public DateTime ImportTime { get; set; }
        public bool IsSuccess { get; set; }
        public int TotalRecords { get; set; }
        public int ImportedRecords { get; set; }
        public string SuccessMessage { get; set; }
        public string ErrorMessage { get; set; }
        public List<string> ValidationErrors { get; set; } = new List<string>();
    }

    /// <summary>
    /// 验证结果
    /// </summary>
    /*public class ValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; }
        public List<string> ValidationErrors { get; set; } = new List<string>();
    }*/
}
