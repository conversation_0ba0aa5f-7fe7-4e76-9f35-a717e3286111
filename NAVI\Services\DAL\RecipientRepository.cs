using System.Data.SQLite;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace NAVI.Services.DAL
{
    /// <summary>
    /// 受给者数据实体类
    /// </summary>
    public class RecipientData
    {
        public int No { get; set; }
        public string 登録日 { get; set; } = string.Empty;
        public string 事業者番号 { get; set; } = string.Empty;
        public string 事業者郵便番号 { get; set; } = string.Empty;
        public string 事業者住所 { get; set; } = string.Empty;
        public string 事業者名称 { get; set; } = string.Empty;
        public string 代表者名 { get; set; } = string.Empty;
        public string 代表者役職 { get; set; } = string.Empty;
        public string サービス提供年月 { get; set; } = string.Empty;
        public string 明細書件数 { get; set; }
        public string 請求金額 { get; set; }
        public string 第三者評価 { get; set; } = string.Empty;
        public string 受給者番号 { get; set; } = string.Empty;
        public string 支給決定障害者氏名 { get; set; } = string.Empty;
        public string 支給決定に係る障害児氏名 { get; set; } = string.Empty;
        public string 障害支援区分 { get; set; } = string.Empty;
        public string 事業者名称2 { get; set; } = string.Empty;
        public string 地域区分 { get; set; } = string.Empty;
        public string 旧身体療護施設区分 { get; set; } = string.Empty;
        public string 精神科医療連携体制加算 { get; set; } = string.Empty;
        public string 開始年月日 { get; set; } = string.Empty;
        public string 終了年月日 { get; set; } = string.Empty;
        public string 利用日数全体 { get; set; }
        public string サービスコード { get; set; } = string.Empty;
        public string サービス内容 { get; set; } = string.Empty;
        public string 算定単価額 { get; set; }
        public string 利用日数 { get; set; }
        public string 当月算定額 { get; set; }
        public string 摘要 { get; set; } = string.Empty;
        public string status { get; set; } = string.Empty;
    }

    /// <summary>
    /// 受给者数据访问类
    /// </summary>
    public class RecipientRepository : BaseRepository<RecipientData>
    {
        public RecipientRepository(DatabaseService databaseService)
            : base(databaseService, "RecipientsData")
        {
        }

        /// <summary>
        /// 根据服务提供年月筛选数据（1-25号）
        /// </summary>
        public async Task<List<RecipientData>> GetByServiceMonthAsync(string serviceMonth)
        {
            try
            {
                // 构建日期范围查询（1-25号）
                var startDate = $"{serviceMonth}-01";
                var endDate = $"{serviceMonth}-25";

                var whereClause = "\"サービス提供年月\" >= @startDate AND \"サービス提供年月\" <= @endDate";
                return await GetByConditionAsync(whereClause,
                    CreateParameter("@startDate", startDate),
                    CreateParameter("@endDate", endDate));
            }
            catch (Exception ex)
            {
                throw new Exception($"根据服务提供年月筛选数据失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据受给者番号获取数据
        /// </summary>
        public async Task<List<RecipientData>> GetByRecipientNumberAsync(string recipientNumber)
        {
            try
            {
                return await GetByConditionAsync("\"受給者番号\" = @recipientNumber",
                    CreateParameter("@recipientNumber", recipientNumber));
            }
            catch (Exception ex)
            {
                throw new Exception($"根据受给者番号获取数据失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据事业者番号获取数据
        /// </summary>
        public async Task<List<RecipientData>> GetByProviderNumberAsync(string providerNumber)
        {
            try
            {
                return await GetByConditionAsync("\"事業者番号\" = @providerNumber",
                    CreateParameter("@providerNumber", providerNumber));
            }
            catch (Exception ex)
            {
                throw new Exception($"根据事业者番号获取数据失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据状态获取数据
        /// </summary>
        public async Task<List<RecipientData>> GetByStatusAsync(string status)
        {
            try
            {
                return await GetByConditionAsync("\"status\" = @status",
                    CreateParameter("@status", status));
            }
            catch (Exception ex)
            {
                throw new Exception($"根据状态获取数据失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 更新状态
        /// </summary>
        public async Task<int> UpdateStatusAsync(int no, string status)
        {
            try
            {
                var data = new RecipientData { status = status };
                return await UpdateAsync(data, "\"No\" = @no", CreateParameter("@no", no));
            }
            catch (Exception ex)
            {
                throw new Exception($"更新状态失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 批量更新状态
        /// </summary>
        public async Task<int> BatchUpdateStatusAsync(List<int> nos, string status)
        {
            try
            {
                var updatedCount = 0;
                foreach (var no in nos)
                {
                    updatedCount += await UpdateStatusAsync(no, status);
                }
                return updatedCount;
            }
            catch (Exception ex)
            {
                throw new Exception($"批量更新状态失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据No获取单条记录
        /// </summary>
        public async Task<RecipientData> GetByNoAsync(int no)
        {
            try
            {
                var results = await GetByConditionAsync("\"No\" = @no", CreateParameter("@no", no));
                return results.FirstOrDefault();
            }
            catch (Exception ex)
            {
                throw new Exception($"根据No获取记录失败: {ex.Message}", ex);
            }
        }

        protected override List<RecipientData> ConvertDataTableToList(DataTable dataTable)
        {
            var dataList = new List<RecipientData>();
            foreach (DataRow row in dataTable.Rows)
            {
                dataList.Add(new RecipientData
                {
                    No = GetIntValue(row, "No"),
                    登録日 = GetStringValue(row, "登録日"),
                    事業者番号 = GetStringValue(row, "事業者番号"),
                    事業者郵便番号 = GetStringValue(row, "事業者郵便番号"),
                    事業者住所 = GetStringValue(row, "事業者住所"),
                    事業者名称 = GetStringValue(row, "事業者名称"),
                    代表者名 = GetStringValue(row, "代表者名"),
                    代表者役職 = GetStringValue(row, "代表者役職"),
                    サービス提供年月 = GetStringValue(row, "サービス提供年月"),
                    明細書件数 = GetStringValue(row, "明細書件数"),
                    請求金額 = GetStringValue(row, "請求金額"),
                    第三者評価 = GetStringValue(row, "第三者評価"),
                    受給者番号 = GetStringValue(row, "受給者番号"),
                    支給決定障害者氏名 = GetStringValue(row, "支給決定障害者氏名"),
                    支給決定に係る障害児氏名 = GetStringValue(row, "支給決定に係る障害児氏名"),
                    障害支援区分 = GetStringValue(row, "障害支援区分"),
                    事業者名称2 = GetStringValue(row, "事業者名称2"),
                    地域区分 = GetStringValue(row, "地域区分"),
                    旧身体療護施設区分 = GetStringValue(row, "旧身体療護施設区分"),
                    精神科医療連携体制加算 = GetStringValue(row, "精神科医療連携体制加算"),
                    開始年月日 = GetStringValue(row, "開始年月日"),
                    終了年月日 = GetStringValue(row, "終了年月日"),
                    利用日数全体 = GetStringValue(row, "利用日数全体"),
                    サービスコード = GetStringValue(row, "サービスコード"),
                    サービス内容 = GetStringValue(row, "サービス内容"),
                    算定単価額 = GetStringValue(row, "算定単価額"),
                    利用日数 = GetStringValue(row, "利用日数"),
                    当月算定額 = GetStringValue(row, "当月算定額"),
                    摘要 = GetStringValue(row, "摘要"),
                    status = GetStringValue(row, "status")
                });
            }
            return dataList;
        }

        protected override DataTable ConvertListToDataTable(List<RecipientData> entities)
        {
            var dataTable = new DataTable();
            var columns = GetColumnNames();

            foreach (var column in columns)
            {
                dataTable.Columns.Add(column);
            }

            foreach (var data in entities)
            {
                var row = dataTable.NewRow();
                row["No"] = data.No;
                row["登録日"] = data.登録日;
                row["事業者番号"] = data.事業者番号;
                row["事業者郵便番号"] = data.事業者郵便番号;
                row["事業者住所"] = data.事業者住所;
                row["事業者名称"] = data.事業者名称;
                row["代表者名"] = data.代表者名;
                row["代表者役職"] = data.代表者役職;
                row["サービス提供年月"] = data.サービス提供年月;
                row["明細書件数"] = data.明細書件数;
                row["請求金額"] = data.請求金額;
                row["第三者評価"] = data.第三者評価;
                row["受給者番号"] = data.受給者番号;
                row["支給決定障害者氏名"] = data.支給決定障害者氏名;
                row["支給決定に係る障害児氏名"] = data.支給決定に係る障害児氏名;
                row["障害支援区分"] = data.障害支援区分;
                row["事業者名称2"] = data.事業者名称2;
                row["地域区分"] = data.地域区分;
                row["旧身体療護施設区分"] = data.旧身体療護施設区分;
                row["精神科医療連携体制加算"] = data.精神科医療連携体制加算;
                row["開始年月日"] = data.開始年月日;
                row["終了年月日"] = data.終了年月日;
                row["利用日数全体"] = data.利用日数全体;
                row["サービスコード"] = data.サービスコード;
                row["サービス内容"] = data.サービス内容;
                row["算定単価額"] = data.算定単価額;
                row["利用日数"] = data.利用日数;
                row["当月算定額"] = data.当月算定額;
                row["摘要"] = data.摘要;
                row["status"] = data.status;
                dataTable.Rows.Add(row);
            }

            return dataTable;
        }

        protected override (string sql, SQLiteParameter[] parameters) BuildInsertCommand(RecipientData entity)
        {
            var sql = @"INSERT INTO RecipientsData 
                (""登録日"", ""事業者番号"", ""事業者郵便番号"", ""事業者住所"", ""事業者名称"", ""代表者名"", ""代表者役職"", ""サービス提供年月"", ""明細書件数"", ""請求金額"", ""第三者評価"", ""受給者番号"", ""支給決定障害者氏名"", ""支給決定に係る障害児氏名"", ""障害支援区分"", ""事業者名称2"", ""地域区分"", ""旧身体療護施設区分"", ""精神科医療連携体制加算"", ""開始年月日"", ""終了年月日"", ""利用日数全体"", ""サービスコード"", ""サービス内容"", ""算定単価額"", ""利用日数"", ""当月算定額"", ""摘要"", ""status"") 
                VALUES (@登録日, @事業者番号, @事業者郵便番号, @事業者住所, @事業者名称, @代表者名, @代表者役職, @サービス提供年月, @明細書件数, @請求金額, @第三者評価, @受給者番号, @支給決定障害者氏名, @支給決定に係る障害児氏名, @障害支援区分, @事業者名称2, @地域区分, @旧身体療護施設区分, @精神科医療連携体制加算, @開始年月日, @終了年月日, @利用日数全体, @サービスコード, @サービス内容, @算定単価額, @利用日数, @当月算定額, @摘要, @status)";

            var parameters = new[]
            {
                CreateParameter("@登録日", entity.登録日),
                CreateParameter("@事業者番号", entity.事業者番号),
                CreateParameter("@事業者郵便番号", entity.事業者郵便番号),
                CreateParameter("@事業者住所", entity.事業者住所),
                CreateParameter("@事業者名称", entity.事業者名称),
                CreateParameter("@代表者名", entity.代表者名),
                CreateParameter("@代表者役職", entity.代表者役職),
                CreateParameter("@サービス提供年月", entity.サービス提供年月),
                CreateParameter("@明細書件数", entity.明細書件数),
                CreateParameter("@請求金額", entity.請求金額),
                CreateParameter("@第三者評価", entity.第三者評価),
                CreateParameter("@受給者番号", entity.受給者番号),
                CreateParameter("@支給決定障害者氏名", entity.支給決定障害者氏名),
                CreateParameter("@支給決定に係る障害児氏名", entity.支給決定に係る障害児氏名),
                CreateParameter("@障害支援区分", entity.障害支援区分),
                CreateParameter("@事業者名称2", entity.事業者名称2),
                CreateParameter("@地域区分", entity.地域区分),
                CreateParameter("@旧身体療護施設区分", entity.旧身体療護施設区分),
                CreateParameter("@精神科医療連携体制加算", entity.精神科医療連携体制加算),
                CreateParameter("@開始年月日", entity.開始年月日),
                CreateParameter("@終了年月日", entity.終了年月日),
                CreateParameter("@利用日数全体", entity.利用日数全体),
                CreateParameter("@サービスコード", entity.サービスコード),
                CreateParameter("@サービス内容", entity.サービス内容),
                CreateParameter("@算定単価額", entity.算定単価額),
                CreateParameter("@利用日数", entity.利用日数),
                CreateParameter("@当月算定額", entity.当月算定額),
                CreateParameter("@摘要", entity.摘要),
                CreateParameter("@status", entity.status)
            };

            return (sql, parameters);
        }

        protected override (string sql, SQLiteParameter[] parameters) BuildUpdateCommand(RecipientData entity, string whereClause, SQLiteParameter[] whereParameters)
        {
            var setParts = new List<string>();
            var parameters = new List<SQLiteParameter>();

            if (!string.IsNullOrEmpty(entity.status))
            {
                setParts.Add("\"status\" = @status");
                parameters.Add(CreateParameter("@status", entity.status));
            }

            // 添加其他可能需要更新的字段
            if (!string.IsNullOrEmpty(entity.登録日))
            {
                setParts.Add("\"登録日\" = @登録日");
                parameters.Add(CreateParameter("@登録日", entity.登録日));
            }
            if (!string.IsNullOrEmpty(entity.事業者番号))
            {
                setParts.Add("\"事業者番号\" = @事業者番号");
                parameters.Add(CreateParameter("@事業者番号", entity.事業者番号));
            }
            if (!string.IsNullOrEmpty(entity.受給者番号))
            {
                setParts.Add("\"受給者番号\" = @受給者番号");
                parameters.Add(CreateParameter("@受給者番号", entity.受給者番号));
            }
            if (!string.IsNullOrEmpty(entity.サービス提供年月))
            {
                setParts.Add("\"サービス提供年月\" = @サービス提供年月");
                parameters.Add(CreateParameter("@サービス提供年月", entity.サービス提供年月));
            }
            if (!string.IsNullOrEmpty(entity.サービスコード))
            {
                setParts.Add("\"サービスコード\" = @サービスコード");
                parameters.Add(CreateParameter("@サービスコード", entity.サービスコード));
            }
            if (!string.IsNullOrEmpty(entity.サービス内容))
            {
                setParts.Add("\"サービス内容\" = @サービス内容");
                parameters.Add(CreateParameter("@サービス内容", entity.サービス内容));
            }
            if (Convert.ToInt32(entity.利用日数) > 0)
            {
                setParts.Add("\"利用日数\" = @利用日数");
                parameters.Add(CreateParameter("@利用日数", entity.利用日数));
            }

            if (whereParameters != null)
            {
                parameters.AddRange(whereParameters);
            }

            var sql = $"UPDATE RecipientsData SET {string.Join(", ", setParts)} WHERE {whereClause}";
            return (sql, parameters.ToArray());
        }

        protected override List<string> GetColumnNames()
        {
            return new List<string>
            {
                "No", "登録日", "事業者番号", "事業者郵便番号", "事業者住所", "事業者名称", "代表者名", "代表者役職", "サービス提供年月", "明細書件数", "請求金額", "第三者評価", "受給者番号", "支給決定障害者氏名", "支給決定に係る障害児氏名", "障害支援区分", "事業者名称2", "地域区分", "旧身体療護施設区分", "精神科医療連携体制加算", "開始年月日", "終了年月日", "利用日数全体", "サービスコード", "サービス内容", "算定単価額", "利用日数", "当月算定額", "摘要", "status"
            };
        }

        /// <summary>
        /// 创建受给者数据
        /// </summary>
        public async Task<RecipientData> CreateRecipientAsync(RecipientData entity)
        {
            try
            {
                var insertedId = await InsertAsync(entity);
                entity.No = (int)insertedId;
                return entity;
            }
            catch (Exception ex)
            {
                throw new Exception($"创建受给者数据失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 更新受给者数据
        /// </summary>
        public async Task<bool> UpdateRecipientAsync(RecipientData entity)
        {
            try
            {
                var result = await UpdateAsync(entity, "\"No\" = @no", CreateParameter("@no", entity.No));
                return result > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"更新受给者数据失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 删除受给者数据
        /// </summary>
        public async Task<bool> DeleteRecipientAsync(int no)
        {
            try
            {
                var result = await DeleteAsync("\"No\" = @no", CreateParameter("@no", no));
                return result > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"删除受给者数据失败: {ex.Message}", ex);
            }
        }
    }
}
