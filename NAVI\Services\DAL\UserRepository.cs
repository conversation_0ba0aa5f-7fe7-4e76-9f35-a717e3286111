using System.Data.SQLite;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace NAVI.Services.DAL
{
    /// <summary>
    /// 用户实体类
    /// </summary>
    public class User
    {
        public string 職員番号 { get; set; } = string.Empty;
        public string 部署名 { get; set; } = string.Empty;
        public string 役職 { get; set; } = string.Empty;
        public string 氏名 { get; set; } = string.Empty;
        public string ID番号 { get; set; } = string.Empty;
        public string パスワード { get; set; } = string.Empty;
        public string 作成日時 { get; set; } = string.Empty;
        public string 更新日時 { get; set; } = string.Empty;
    }

    /// <summary>
    /// 用户数据访问类
    /// </summary>
    public class UserRepository : BaseRepository<User>
    {
        public UserRepository(DatabaseService databaseService) 
            : base(databaseService, "Users")
        {
        }

        /// <summary>
        /// 根据职员番号获取用户
        /// </summary>
        public async Task<User> GetByEmployeeNumberAsync(string employeeNumber)
        {
            try
            {
                var users = await GetByConditionAsync("\"職員番号\" = @employeeNumber", 
                    CreateParameter("@employeeNumber", employeeNumber));
                return users.FirstOrDefault();
            }
            catch (Exception ex)
            {
                throw new Exception($"根据职员番号获取用户失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据ID番号获取用户
        /// </summary>
        public async Task<User> GetByIdNumberAsync(string idNumber)
        {
            try
            {
                var users = await GetByConditionAsync("\"ID番号\" = @idNumber", 
                    CreateParameter("@idNumber", idNumber));
                return users.FirstOrDefault();
            }
            catch (Exception ex)
            {
                throw new Exception($"根据ID番号获取用户失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 验证用户登录
        /// </summary>
        public async Task<User> ValidateLoginAsync(string employeeNumber, string password)
        {
            try
            {
                // 首先检查用户是否存在
                var users = await GetByConditionAsync("\"職員番号\" = @employeeNumber",
                    CreateParameter("@employeeNumber", employeeNumber));

                var user = users.FirstOrDefault();
                if (user == null)
                {
                    return null; // 用户不存在
                }

                // 验证密码
                if (user.パスワード != password)
                {
                    return null; // 密码错误
                }

                // 更新最后登录时间
                await UpdateLastLoginTimeAsync(employeeNumber);

                return user;
            }
            catch (Exception ex)
            {
                throw new Exception($"验证用户登录失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 验证用户登录（支持ID番号登录）
        /// </summary>
        public async Task<User> ValidateLoginByIdAsync(string idNumber, string password)
        {
            try
            {
                // 首先检查用户是否存在
                var users = await GetByConditionAsync("\"ID番号\" = @idNumber",
                    CreateParameter("@idNumber", idNumber));

                var user = users.FirstOrDefault();
                if (user == null)
                {
                    return null; // 用户不存在
                }

                // 验证密码
                if (user.パスワード != password)
                {
                    return null; // 密码错误
                }

                // 更新最后登录时间
                await UpdateLastLoginTimeAsync(user.職員番号);

                return user;
            }
            catch (Exception ex)
            {
                throw new Exception($"验证用户登录失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 更新最后登录时间
        /// </summary>
        private async Task UpdateLastLoginTimeAsync(string employeeNumber)
        {
            try
            {
                var user = new User { 更新日時 = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") };
                await UpdateAsync(user, "\"職員番号\" = @employeeNumber",
                    CreateParameter("@employeeNumber", employeeNumber));
            }
            catch (Exception ex)
            {
                // 记录日志但不抛出异常，避免影响登录流程
                System.Diagnostics.Debug.WriteLine($"更新最后登录时间失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新用户信息
        /// </summary>
        public async Task<bool> UpdateUserAsync(User entity)
        {
            try
            {
                var result = await UpdateAsync(entity, "\"職員番号\" = @employeeNumber",
                    CreateParameter("@employeeNumber", entity.職員番号));
                return result > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"更新用户信息失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 更新用户密码
        /// </summary>
        public async Task<int> UpdatePasswordAsync(string employeeNumber, string newPassword)
        {
            try
            {
                var user = new User { パスワード = newPassword, 更新日時 = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") };
                return await UpdateAsync(user, "\"職員番号\" = @employeeNumber", 
                    CreateParameter("@employeeNumber", employeeNumber));
            }
            catch (Exception ex)
            {
                throw new Exception($"更新用户密码失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 检查职员番号是否存在
        /// </summary>
        public async Task<bool> EmployeeNumberExistsAsync(string employeeNumber)
        {
            try
            {
                return await ExistsAsync("\"職員番号\" = @employeeNumber", 
                    CreateParameter("@employeeNumber", employeeNumber));
            }
            catch (Exception ex)
            {
                throw new Exception($"检查职员番号存在性失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 检查ID番号是否存在
        /// </summary>
        public async Task<bool> IdNumberExistsAsync(string idNumber)
        {
            try
            {
                return await ExistsAsync("\"ID番号\" = @idNumber", 
                    CreateParameter("@idNumber", idNumber));
            }
            catch (Exception ex)
            {
                throw new Exception($"检查ID番号存在性失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 创建新用户
        /// </summary>
        public async Task<int> CreateUserAsync(User user)
        {
            try
            {
                user.作成日時 = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                user.更新日時 = user.作成日時;
                return await InsertAsync(user);
            }
            catch (Exception ex)
            {
                throw new Exception($"创建用户失败: {ex.Message}", ex);
            }
        }

        protected override List<User> ConvertDataTableToList(DataTable dataTable)
        {
            var users = new List<User>();
            foreach (DataRow row in dataTable.Rows)
            {
                users.Add(new User
                {
                    職員番号 = GetStringValue(row, "職員番号"),
                    部署名 = GetStringValue(row, "部署名"),
                    役職 = GetStringValue(row, "役職"),
                    氏名 = GetStringValue(row, "氏名"),
                    ID番号 = GetStringValue(row, "ID番号"),
                    パスワード = GetStringValue(row, "パスワード"),
                    作成日時 = GetStringValue(row, "作成日時"),
                    更新日時 = GetStringValue(row, "更新日時")
                });
            }
            return users;
        }

        protected override DataTable ConvertListToDataTable(List<User> entities)
        {
            var dataTable = new DataTable();
            var columns = GetColumnNames();
            
            foreach (var column in columns)
            {
                dataTable.Columns.Add(column);
            }

            foreach (var user in entities)
            {
                var row = dataTable.NewRow();
                row["職員番号"] = user.職員番号;
                row["部署名"] = user.部署名;
                row["役職"] = user.役職;
                row["氏名"] = user.氏名;
                row["ID番号"] = user.ID番号;
                row["パスワード"] = user.パスワード;
                row["作成日時"] = user.作成日時;
                row["更新日時"] = user.更新日時;
                dataTable.Rows.Add(row);
            }

            return dataTable;
        }

        protected override (string sql, SQLiteParameter[] parameters) BuildInsertCommand(User entity)
        {
            var sql = @"INSERT INTO Users 
                (""職員番号"", ""部署名"", ""役職"", ""氏名"", ""ID番号"", ""パスワード"", ""作成日時"", ""更新日時"") 
                VALUES (@職員番号, @部署名, @役職, @氏名, @ID番号, @パスワード, @作成日時, @更新日時)";

            var parameters = new[]
            {
                CreateParameter("@職員番号", entity.職員番号),
                CreateParameter("@部署名", entity.部署名),
                CreateParameter("@役職", entity.役職),
                CreateParameter("@氏名", entity.氏名),
                CreateParameter("@ID番号", entity.ID番号),
                CreateParameter("@パスワード", entity.パスワード),
                CreateParameter("@作成日時", entity.作成日時),
                CreateParameter("@更新日時", entity.更新日時)
            };

            return (sql, parameters);
        }

        protected override (string sql, SQLiteParameter[] parameters) BuildUpdateCommand(User entity, string whereClause, SQLiteParameter[] whereParameters)
        {
            var setParts = new List<string>();
            var parameters = new List<SQLiteParameter>();

            if (!string.IsNullOrEmpty(entity.部署名))
            {
                setParts.Add("\"部署名\" = @部署名");
                parameters.Add(CreateParameter("@部署名", entity.部署名));
            }
            if (!string.IsNullOrEmpty(entity.役職))
            {
                setParts.Add("\"役職\" = @役職");
                parameters.Add(CreateParameter("@役職", entity.役職));
            }
            if (!string.IsNullOrEmpty(entity.氏名))
            {
                setParts.Add("\"氏名\" = @氏名");
                parameters.Add(CreateParameter("@氏名", entity.氏名));
            }
            if (!string.IsNullOrEmpty(entity.ID番号))
            {
                setParts.Add("\"ID番号\" = @ID番号");
                parameters.Add(CreateParameter("@ID番号", entity.ID番号));
            }
            if (!string.IsNullOrEmpty(entity.パスワード))
            {
                setParts.Add("\"パスワード\" = @パスワード");
                parameters.Add(CreateParameter("@パスワード", entity.パスワード));
            }
            if (!string.IsNullOrEmpty(entity.更新日時))
            {
                setParts.Add("\"更新日時\" = @更新日時");
                parameters.Add(CreateParameter("@更新日時", entity.更新日時));
            }

            if (whereParameters != null)
            {
                parameters.AddRange(whereParameters);
            }

            var sql = $"UPDATE Users SET {string.Join(", ", setParts)} WHERE {whereClause}";
            return (sql, parameters.ToArray());
        }

        protected override List<string> GetColumnNames()
        {
            return new List<string>
            {
                "職員番号", "部署名", "役職", "氏名", "ID番号", "パスワード", "作成日時", "更新日時"
            };
        }
    }
}
