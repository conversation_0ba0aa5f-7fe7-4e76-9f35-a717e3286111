using NAVI.Services.DAL;
using System;

namespace NAVI.Services
{
    /// <summary>
    /// 数据库管理器 - 统一管理所有数据访问层
    /// </summary>
    public class DatabaseManager : IDisposable
    {
        private readonly DatabaseService _databaseService;
        private bool _disposed = false;

        // 数据访问层实例
        private UserRepository _userRepository;
        private KokuhoRenRepository _kokuhoRenRepository;
        private RecipientRepository _recipientRepository;
        private ServiceProviderRepository _serviceProviderRepository;
        private ServiceCodeRepository _serviceCodeRepository;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="databasePath">数据库文件路径</param>
        public DatabaseManager(string databasePath = null)
        {
            _databaseService = new DatabaseService(databasePath);
            InitializeRepositories();
        }

        /// <summary>
        /// 用户数据访问层
        /// </summary>
        public UserRepository Users
        {
            get
            {
                if (_userRepository == null)
                    _userRepository = new UserRepository(_databaseService);
                return _userRepository;
            }
        }

        /// <summary>
        /// 国保联数据访问层
        /// </summary>
        public KokuhoRenRepository KokuhoRenData
        {
            get
            {
                if (_kokuhoRenRepository == null)
                    _kokuhoRenRepository = new KokuhoRenRepository(_databaseService);
                return _kokuhoRenRepository;
            }
        }

        /// <summary>
        /// 受给者数据访问层
        /// </summary>
        public RecipientRepository Recipients
        {
            get
            {
                if (_recipientRepository == null)
                    _recipientRepository = new RecipientRepository(_databaseService);
                return _recipientRepository;
            }
        }

        /// <summary>
        /// 服务提供者数据访问层
        /// </summary>
        public ServiceProviderRepository ServiceProviders
        {
            get
            {
                if (_serviceProviderRepository == null)
                    _serviceProviderRepository = new ServiceProviderRepository(_databaseService);
                return _serviceProviderRepository;
            }
        }

        /// <summary>
        /// 服务代码主数据访问层
        /// </summary>
        public ServiceCodeRepository ServiceCodes
        {
            get
            {
                if (_serviceCodeRepository == null)
                    _serviceCodeRepository = new ServiceCodeRepository(_databaseService);
                return _serviceCodeRepository;
            }
        }

        /// <summary>
        /// 获取数据库服务实例（用于直接执行SQL）
        /// </summary>
        public DatabaseService Database => _databaseService;

        /// <summary>
        /// 初始化所有数据访问层
        /// </summary>
        private void InitializeRepositories()
        {
            // 延迟初始化，在首次访问时创建
        }

        /// <summary>
        /// 测试数据库连接
        /// </summary>
        public bool TestConnection()
        {
            try
            {
                var result = _databaseService.ExecuteScalarAsync("SELECT 1").Result;
                return result != null && result.ToString() == "1";
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取数据库版本信息
        /// </summary>
        public string GetDatabaseVersion()
        {
            try
            {
                var result = _databaseService.ExecuteScalarAsync("SELECT sqlite_version()").Result;
                return result?.ToString() ?? "Unknown";
            }
            catch
            {
                return "Unknown";
            }
        }

        /// <summary>
        /// 获取表列表
        /// </summary>
        public string[] GetTableNames()
        {
            try
            {
                var dataTable = _databaseService.ExecuteQueryAsync(
                    "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name").Result;
                
                var tableNames = new string[dataTable.Rows.Count];
                for (int i = 0; i < dataTable.Rows.Count; i++)
                {
                    tableNames[i] = dataTable.Rows[i]["name"].ToString();
                }
                return tableNames;
            }
            catch
            {
                return new string[0];
            }
        }

        /// <summary>
        /// 获取表的记录数
        /// </summary>
        public int GetTableRowCount(string tableName)
        {
            try
            {
                var result = _databaseService.ExecuteScalarAsync($"SELECT COUNT(*) FROM {tableName}").Result;
                return Convert.ToInt32(result);
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// 清空所有表数据（保留表结构）
        /// </summary>
        public void ClearAllData()
        {
            try
            {
                var tableNames = GetTableNames();
                foreach (var tableName in tableNames)
                {
                    if (tableName != "sqlite_sequence") // 跳过系统表
                    {
                        _databaseService.ExecuteNonQueryAsync($"DELETE FROM {tableName}").Wait();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"清空所有表数据失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 备份数据库
        /// </summary>
        public void BackupDatabase(string backupPath)
        {
            try
            {
                // SQLite数据库文件可以直接复制
                var currentDbPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "database", "navi.db");
                if (System.IO.File.Exists(currentDbPath))
                {
                    System.IO.File.Copy(currentDbPath, backupPath, true);
                }
                else
                {
                    throw new Exception("数据库文件不存在");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"备份数据库失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 恢复数据库
        /// </summary>
        public void RestoreDatabase(string backupPath)
        {
            try
            {
                if (!System.IO.File.Exists(backupPath))
                {
                    throw new Exception("备份文件不存在");
                }

                var currentDbPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "database", "navi.db");
                
                // 关闭当前连接
                _databaseService?.Dispose();
                
                // 复制备份文件
                System.IO.File.Copy(backupPath, currentDbPath, true);
                
                // 重新初始化数据库服务
                var newDatabaseService = new DatabaseService();
                
                // 这里需要重新创建DatabaseManager实例，因为无法直接替换_databaseService
                throw new Exception("恢复数据库后需要重新创建DatabaseManager实例");
            }
            catch (Exception ex)
            {
                throw new Exception($"恢复数据库失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 执行数据库维护操作
        /// </summary>
        public void MaintenanceDatabase()
        {
            try
            {
                // 执行VACUUM命令来优化数据库
                _databaseService.ExecuteNonQueryAsync("VACUUM").Wait();
                
                // 分析数据库统计信息
                _databaseService.ExecuteNonQueryAsync("ANALYZE").Wait();
            }
            catch (Exception ex)
            {
                throw new Exception($"数据库维护失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _databaseService?.Dispose();
                }
                _disposed = true;
            }
        }

        ~DatabaseManager()
        {
            Dispose(false);
        }
    }
}
