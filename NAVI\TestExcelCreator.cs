using System;
using System.IO;
using NPOI.XSSF.UserModel;
using NPOI.SS.UserModel;

namespace NAVI
{
    /// <summary>
    /// 测试Excel文件创建器
    /// </summary>
    public static class TestExcelCreator
    {
        /// <summary>
        /// 创建测试Excel文件
        /// </summary>
        public static void CreateTestExcelFile()
        {
            var workbook = new XSSFWorkbook();
            
            // 创建事业者数据工作表
            CreateBusinessDataSheet(workbook);
            
            // 创建国保联数据工作表
            CreateNationalDataSheet(workbook);
            
            // 创建服务代码数据工作表
            CreateServiceCodeDataSheet(workbook);

            // 创建受给者服务信息工作表
            CreateRecipientServiceInfoSheet(workbook);

            // 保存文件
            var filePath = Path.Combine("database", "shortstay_app_ver0.9.xlsx");
            Directory.CreateDirectory(Path.GetDirectoryName(filePath));
            
            using (var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write))
            {
                workbook.Write(fileStream);
            }
            
            workbook.Close();
        }
        
        /// <summary>
        /// 创建事业者数据工作表
        /// </summary>
        private static void CreateBusinessDataSheet(IWorkbook workbook)
        {
            var sheet = workbook.CreateSheet("事业者数据");
            
            // 创建表头
            var headerRow = sheet.CreateRow(0);
            var headers = new[] { "事业者编号", "邮政编码", "事业者名称", "代表者职务", "代表者姓名", "负责人姓名", "联系方式", "服务类别", "附加服务对象", "第三方评价结果", "培训受讲说明", "所在地地址", "用户信息" };
            
            for (int i = 0; i < headers.Length; i++)
            {
                headerRow.CreateCell(i).SetCellValue(headers[i]);
            }
            
            // 添加示例数据
            var sampleData = new[]
            {
                new[] { "wqef453etf", "168-8577", "サポートハウス東京", "代表取締役社長", "中村美咲", "鈴木太郎", "03-1234-5678", "共同生活援助", "医疗连携体制补贴", "2sfewrew", "外部研修受講済 2025年3月", "東京都杉並区高井戸1-2-3", "20名（年齢: 平均40歳、男女比: 6:4）" },
                new[] { "abc123def", "160-0023", "ケアホーム新宿", "代表取締役", "田中花子", "佐藤次郎", "03-2345-6789", "居宅介護", "重度訪問介護", "評価A", "内部研修実施済", "東京都新宿区西新宿2-1-1", "15名（年齢: 平均35歳、男女比: 7:3）" },
                new[] { "xyz789ghi", "150-0002", "グループホーム渋谷", "施設長", "山田太郎", "高橋三郎", "03-3456-7890", "共同生活援助", "行動援護", "評価B", "外部研修予定", "東京都渋谷区渋谷1-1-1", "25名（年齢: 平均42歳、男女比: 5:5）" }
            };
            
            for (int i = 0; i < sampleData.Length; i++)
            {
                var row = sheet.CreateRow(i + 1);
                for (int j = 0; j < sampleData[i].Length; j++)
                {
                    row.CreateCell(j).SetCellValue(sampleData[i][j]);
                }
            }
        }
        
        /// <summary>
        /// 创建国保联数据工作表
        /// </summary>
        private static void CreateNationalDataSheet(IWorkbook workbook)
        {
            var sheet = workbook.CreateSheet("国保联数据");
            
            // 创建表头
            var headerRow = sheet.CreateRow(0);
            var headers = new[] { "序号", "保险者番号", "被保险者番号", "氏名", "生年月日", "性别", "住所", "电话番号", "认定年月日", "有效期限" };
            
            for (int i = 0; i < headers.Length; i++)
            {
                headerRow.CreateCell(i).SetCellValue(headers[i]);
            }
            
            // 添加示例数据
            var sampleData = new[]
            {
                new[] { "1", "131016", "1234567890", "田中太郎", "1980/04/15", "男", "东京都新宿区西新宿1-1-1", "03-1234-5678", "2023/04/01", "2024/03/31" },
                new[] { "2", "131016", "2345678901", "佐藤花子", "1975/08/22", "女", "东京都渋谷区渋谷2-2-2", "03-2345-6789", "2023/05/15", "2024/05/14" },
                new[] { "3", "131024", "3456789012", "铃木一郎", "1965/12/03", "男", "东京都品川区大井1-3-3", "03-3456-7890", "2023/06/01", "2024/05/31" },
                new[] { "4", "131024", "4567890123", "高橋美香", "1970/09/10", "女", "东京都港区赤坂1-4-4", "03-4567-8901", "2023/07/01", "2024/06/30" },
                new[] { "5", "131032", "5678901234", "山田健太", "1985/01/25", "男", "东京都中央区银座1-5-5", "03-5678-9012", "2023/08/01", "2024/07/31" }
            };
            
            for (int i = 0; i < sampleData.Length; i++)
            {
                var row = sheet.CreateRow(i + 1);
                for (int j = 0; j < sampleData[i].Length; j++)
                {
                    row.CreateCell(j).SetCellValue(sampleData[i][j]);
                }
            }
        }
        
        /// <summary>
        /// 创建服务代码数据工作表
        /// </summary>
        private static void CreateServiceCodeDataSheet(IWorkbook workbook)
        {
            var sheet = workbook.CreateSheet("服务代码数据");
            
            // 创建表头
            var headerRow = sheet.CreateRow(0);
            var headers = new[] { "服务代码", "服务名称", "服务分类", "单位", "基本报酬", "地域加算", "备注", "有效期间开始", "有效期间结束" };
            
            for (int i = 0; i < headers.Length; i++)
            {
                headerRow.CreateCell(i).SetCellValue(headers[i]);
            }
            
            // 添加示例数据
            var sampleData = new[]
            {
                new[] { "110101", "居宅介护", "居宅服务", "回", "2500", "有", "基本的な身体介護サービス", "2023/04/01", "2024/03/31" },
                new[] { "110201", "居宅介护（生活援助）", "居宅服务", "回", "1800", "有", "生活援助中心のサービス", "2023/04/01", "2024/03/31" },
                new[] { "120101", "重度访问介护", "居宅服务", "时间", "4200", "有", "重度の障害者向けサービス", "2023/04/01", "2024/03/31" },
                new[] { "130101", "同行援护", "居宅服务", "回", "3200", "无", "外出時の同行支援サービス", "2023/04/01", "2024/03/31" },
                new[] { "210101", "生活介护", "日间活动", "日", "6800", "有", "日中活動支援サービス", "2023/04/01", "2024/03/31" },
                new[] { "220101", "就労移行支援", "日间活动", "日", "5400", "有", "就労に向けた支援サービス", "2023/04/01", "2024/03/31" },
                new[] { "230101", "就労継続支援A型", "日间活动", "日", "4800", "有", "雇用契約に基づく就労支援", "2023/04/01", "2024/03/31" },
                new[] { "240101", "就労継続支援B型", "日间活动", "日", "3600", "有", "非雇用型の就労支援", "2023/04/01", "2024/03/31" }
            };
            
            for (int i = 0; i < sampleData.Length; i++)
            {
                var row = sheet.CreateRow(i + 1);
                for (int j = 0; j < sampleData[i].Length; j++)
                {
                    row.CreateCell(j).SetCellValue(sampleData[i][j]);
                }
            }
        }

        /// <summary>
        /// 创建受给者服务信息工作表
        /// </summary>
        private static void CreateRecipientServiceInfoSheet(IWorkbook workbook)
        {
            var sheet = workbook.CreateSheet("受给者数据");

            // 创建表头
            var headerRow = sheet.CreateRow(0);
            var headers = new[] { "受给者ID", "受给者姓名", "生年月日", "性别", "住所", "电话番号", "服务代码", "服务名称", "利用开始日", "利用结束日", "月利用回数", "单价", "月额费用", "负担额", "状态", "备注" };

            for (int i = 0; i < headers.Length; i++)
            {
                headerRow.CreateCell(i).SetCellValue(headers[i]);
            }

            // 添加示例数据
            var sampleData = new[]
            {
                new[] { "R001", "田中太郎", "1980/04/15", "男", "东京都新宿区西新宿1-1-1", "03-1234-5678", "110101", "居宅介护", "2023/04/01", "2024/03/31", "20", "2500", "50000", "5000", "利用中", "月20回利用予定" },
                new[] { "R002", "佐藤花子", "1975/08/22", "女", "东京都渋谷区渋谷2-2-2", "03-2345-6789", "110201", "居宅介护（生活援助）", "2023/05/01", "2024/04/30", "15", "1800", "27000", "2700", "利用中", "生活援助中心" },
                new[] { "R003", "铃木一郎", "1965/12/03", "男", "东京都品川区大井1-3-3", "03-3456-7890", "120101", "重度访问介护", "2023/06/01", "2024/05/31", "30", "4200", "126000", "12600", "利用中", "重度障害者支援" },
                new[] { "R004", "高橋美香", "1970/09/10", "女", "东京都港区赤坂1-4-4", "03-4567-8901", "130101", "同行援护", "2023/07/01", "2024/06/30", "8", "3200", "25600", "2560", "利用中", "外出支援" },
                new[] { "R005", "山田健太", "1985/01/25", "男", "东京都中央区银座1-5-5", "03-5678-9012", "210101", "生活介护", "2023/08/01", "2024/07/31", "22", "6800", "149600", "14960", "利用中", "日中活動支援" },
                new[] { "R006", "中村美咲", "1990/03/12", "女", "东京都杉并区高井户1-2-3", "03-6789-0123", "220101", "就労移行支援", "2023/09/01", "2024/08/31", "20", "5400", "108000", "10800", "利用中", "就労支援" },
                new[] { "R007", "小林健", "1988/11/08", "男", "东京都世田谷区三轩茶屋1-1-1", "03-7890-1234", "230101", "就労継続支援A型", "2023/10/01", "2024/09/30", "20", "4800", "96000", "9600", "利用中", "雇用契約型" },
                new[] { "R008", "伊藤由美", "1982/07/20", "女", "东京都目黑区自由之丘2-2-2", "03-8901-2345", "240101", "就労継続支援B型", "2023/11/01", "2024/10/31", "18", "3600", "64800", "6480", "利用中", "非雇用型" }
            };

            for (int i = 0; i < sampleData.Length; i++)
            {
                var row = sheet.CreateRow(i + 1);
                for (int j = 0; j < sampleData[i].Length; j++)
                {
                    row.CreateCell(j).SetCellValue(sampleData[i][j]);
                }
            }
        }
    }
}
