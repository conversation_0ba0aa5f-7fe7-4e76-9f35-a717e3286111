<UserControl x:Class="NAVI.UserManagementControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:controls="clr-namespace:NAVI.Controls"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000">
   
        <UserControl.Resources>
            <!-- 现代化按钮样式 -->
            <Style x:Key="ModernButtonStyle" TargetType="Button">
                <Setter Property="Background" Value="#FF2986A8"/>
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Padding" Value="16,8"/>
                <Setter Property="FontSize" Value="13"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="Cursor" Value="Hand"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                               CornerRadius="4"
                               Padding="{TemplateBinding Padding}">
                                <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#FF1976D2"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="#FF1565C0"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- 次要按钮样式 -->
            <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
                <Setter Property="Background" Value="#FF757575"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="#FF616161"/>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!-- 危险按钮样式 -->
            <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
                <Setter Property="Background" Value="#FFF44336"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="#FFD32F2F"/>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!-- 成功按钮样式 -->
            <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
                <Setter Property="Background" Value="#FF4CAF50"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="#FF388E3C"/>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!-- 现代化文本框样式 -->
            <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
                <Setter Property="Background" Value="White"/>
                <Setter Property="BorderBrush" Value="#FFCCCCCC"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Padding" Value="12,8"/>
                <Setter Property="FontSize" Value="13"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="TextBox">
                            <Border Background="{TemplateBinding Background}"
                               BorderBrush="{TemplateBinding BorderBrush}"
                               BorderThickness="{TemplateBinding BorderThickness}"
                               CornerRadius="4">
                                <ScrollViewer x:Name="PART_ContentHost"
                                         Margin="{TemplateBinding Padding}"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsFocused" Value="True">
                                    <Setter Property="BorderBrush" Value="#FF2986A8"/>
                                    <Setter Property="BorderThickness" Value="2"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- DataGrid样式 -->
            <Style x:Key="ModernDataGridStyle" TargetType="DataGrid">
                <Setter Property="Background" Value="White"/>
                <Setter Property="BorderBrush" Value="#FFE0E0E0"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="GridLinesVisibility" Value="Horizontal"/>
                <Setter Property="HorizontalGridLinesBrush" Value="#FFF0F0F0"/>
                <Setter Property="RowBackground" Value="White"/>
                <Setter Property="AlternatingRowBackground" Value="#FFF8F9FA"/>
                <Setter Property="HeadersVisibility" Value="Column"/>
                <Setter Property="CanUserResizeRows" Value="False"/>
                <Setter Property="CanUserAddRows" Value="False"/>
                <Setter Property="AutoGenerateColumns" Value="False"/>
                <Setter Property="SelectionMode" Value="Single"/>
                <Setter Property="SelectionUnit" Value="FullRow"/>
                <Setter Property="RowHeight" Value="50"/>
                <Setter Property="FontSize" Value="13"/>
                <Setter Property="ColumnWidth" Value="*"/>
            </Style>

            <!-- DataGrid行样式 -->
            <Style x:Key="ModernDataGridRowStyle" TargetType="DataGridRow">
                <Setter Property="Height" Value="50"/>
                <Setter Property="Background" Value="White"/>
                <Setter Property="BorderThickness" Value="0"/>
                <!-- 添加边框 -->
                <Setter Property="BorderBrush" Value="Black"/>
                <!-- 设置颜色为黑色 -->
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="#FFF5F5F5"/>
                    </Trigger>
                    <Trigger Property="IsSelected" Value="True">
                        <Setter Property="Background" Value="#FFE3F2FD"/>
                        <Setter Property="BorderBrush" Value="#FF2196F3"/>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!-- DataGrid列标题样式 -->
            <Style x:Key="ModernDataGridColumnHeaderStyle" TargetType="DataGridColumnHeader">
                <Setter Property="Background" Value="#FF37474F"/>
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="FontSize" Value="13"/>
                <Setter Property="Padding" Value="8,6"/>
                <Setter Property="BorderThickness" Value="0,0,1,0"/>
                <Setter Property="BorderBrush" Value="#FF546E7A"/>
                <Setter Property="Height" Value="50"/>
            </Style>

            <Style x:Key="ModernDataGridCellStyle" TargetType="DataGridCell">
                <Setter Property="BorderBrush" Value="Black"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Padding" Value="5"/>
                <Setter Property="VerticalAlignment" Value="Center"/>
                <Setter Property="HorizontalAlignment" Value="Stretch"/>
                <Setter Property="Background" Value="White"/>

                <Style.Triggers>
                    <!-- 鼠标悬停 -->
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="#FFF5F5F5"/>
                    </Trigger>

                    <!-- 选中单元格 -->
                    <Trigger Property="IsSelected" Value="True">
                        <Setter Property="Background" Value="#FFE3F2FD"/>
                        <Setter Property="BorderBrush" Value="#FF2196F3"/>
                    </Trigger>
                </Style.Triggers>
            </Style>
        </UserControl.Resources>
    <Grid Background="#FFF8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 顶部内容区 -->
        <Border Grid.Row="0"
               Background="White"
               BorderBrush="#FFE0E0E0"
               BorderThickness="0,0,0,1"
               Padding="24,20">
            <StackPanel>
                <!-- 页面标题 -->
                <TextBlock Text="職員マスタデータ"
                          FontSize="24"
                          FontWeight="Medium"
                          Foreground="#FF212121"
                          Margin="0,0,0,20"/>

                <!-- 操作按钮区域 -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                    <!-- 新增按钮 -->
                    <Button x:Name="AddButton" Content="新規職員追加"
                           Style="{StaticResource ModernButtonStyle}"
                           Width="120" Height="36"
                           Margin="0,0,12,0"
                           Click="AddButton_Click"/>

                    <!-- 搜索文本框 -->
                    <TextBox x:Name="SearchTextBox"
                            Width="250"
                            Height="36"
                            Style="{StaticResource ModernTextBoxStyle}"
                            VerticalAlignment="Center"
                            Margin="0,0,12,0"
                            Text="職員番号・氏名・役職で検索"
                            GotFocus="SearchTextBox_GotFocus"
                            LostFocus="SearchTextBox_LostFocus"/>

                    <!-- 搜索按钮 -->
                    <Button x:Name="SearchButton" Content="検索"
                           Style="{StaticResource ModernButtonStyle}"
                           Width="80" Height="36"
                           Margin="0,0,12,0"
                           Click="SearchButton_Click"/>

                    <!-- 导出按钮 -->
                    <Button x:Name="ExportButton" Content="エクスポート"
                           Style="{StaticResource SecondaryButtonStyle}"
                           Width="100" Height="36"
                           Margin="0,0,12,0"
                           Click="ExportButton_Click"/>

                    <!-- 打印按钮 -->
                    <Button x:Name="PrintButton" Content="印刷"
                           Style="{StaticResource SecondaryButtonStyle}"
                           Width="80" Height="36"
                           Margin="0,0,12,0"
                           Click="PrintButton_Click"/>

                    <!-- 刷新按钮 -->
                    <Button x:Name="RefreshButton" Content="更新"
                           Style="{StaticResource SecondaryButtonStyle}"
                           Width="80" Height="36"
                           Margin="0,0,12,0"
                           Click="RefreshButton_Click"/>

                    <!-- CSV导入按钮 -->
                    <Button x:Name="ImportCsvButton" Content="CSV取込"
                           Style="{StaticResource SuccessButtonStyle}"
                           Width="100" Height="36"
                           Click="ImportCsvButton_Click"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- 数据表格区域 -->
        <Border Grid.Row="1"
               Background="White"
               Margin="24,16,24,0"
               CornerRadius="8,8,0,0"
               BorderBrush="#FFE0E0E0"
               BorderThickness="1,1,1,0">
            <DataGrid x:Name="UserDataGrid"
                     Style="{StaticResource ModernDataGridStyle}"
                     ColumnHeaderStyle="{StaticResource ModernDataGridColumnHeaderStyle}"
                     Margin="0">
                <DataGrid.Columns>
                    <!-- 操作列 -->
                    <DataGridTemplateColumn Header="操作" Width="150" CanUserSort="False">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="2">
                                    <Button Content="編集" Width="60" Height="30" FontSize="12"
                                           Style="{StaticResource ModernButtonStyle}" Margin="0,0,4,0"
                                           Tag="{Binding}"
                                           Click="EditButton_Click"/>
                                    <Button Content="削除" Width="60" Height="30" FontSize="12"
                                           Style="{StaticResource DangerButtonStyle}"
                                           Tag="{Binding}"
                                           Click="DeleteButton_Click"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- 职员番号 -->
                    <DataGridTextColumn Header="職員番号" Binding="{Binding 職員番号}" Width="*" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="FontSize" Value="13"/>
                                <Setter Property="FontWeight" Value="Medium"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 部署名 -->
                    <DataGridTextColumn Header="部署名" Binding="{Binding 部署名}" Width="*" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="FontSize" Value="13"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 役职 -->
                    <DataGridTextColumn Header="役職" Binding="{Binding 役職}" Width="*" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="FontSize" Value="13"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 氏名 -->
                    <DataGridTextColumn Header="氏名" Binding="{Binding 氏名}" Width="*" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="FontSize" Value="13"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- ID番号 -->
                    <DataGridTextColumn Header="ID番号" Binding="{Binding ID番号}" Width="*" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="FontSize" Value="13"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Border>

        <!-- 分页控件 -->
        <controls:PaginationControl x:Name="PaginationControl"
                                   Grid.Row="2"
                                   Margin="24,0,24,24"
                                   PageChanged="PaginationControl_PageChanged"
                                   PageSizeChanged="PaginationControl_PageSizeChanged"/>
    </Grid>
</UserControl>
