﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace NAVI.Utils
{
    public static class ClaudeOcrHelper
    {
        // API配置
        public static string ApiKey { get; set; } = "************************************************************************************************************";
        public static string ApiUrl { get; set; } = "https://api.anthropic.com/v1/messages";
        public static string Model { get; set; } = "claude-sonnet-4-20250514";

        private static readonly HttpClient _httpClient = new HttpClient();

        private static readonly Dictionary<string, string> MimeTypes = new Dictionary<string, string>
        {
            [".png"] = "image/png",
            [".jpg"] = "image/jpeg",
            [".jpeg"] = "image/jpeg",
            [".gif"] = "image/gif",
            [".bmp"] = "image/bmp",
            [".webp"] = "image/webp"
        };

        public static Tuple<string, string> EncodeImageToBase64(string imagePath)
        {
            if (!File.Exists(imagePath))
                throw new FileNotFoundException($"图片文件不存在: {imagePath}");

            string extension = Path.GetExtension(imagePath).ToLower();
            if (!MimeTypes.TryGetValue(extension, out string mimeType))
                mimeType = "image/jpeg";

            byte[] imageBytes = File.ReadAllBytes(imagePath);
            return Tuple.Create(Convert.ToBase64String(imageBytes), mimeType);
        }

        public static string CreateOcrPrompt()
        {
            var prompt = new StringBuilder();

            prompt.AppendLine("请仔细识别这张日本政府福祉服务相关的表格图片，提取所有信息并以JSON格式返回。");
            prompt.AppendLine();
            prompt.AppendLine("请识别表格中的所有字段，包括但不限于：");
            prompt.AppendLine();
            prompt.AppendLine("1. 头部信息：");
            prompt.AppendLine("   - 标题（都加算明細書）");
            prompt.AppendLine("   - 日期（令和年月分）");
            prompt.AppendLine();
            prompt.AppendLine("2. 受给者信息：");
            prompt.AppendLine("   - 受給者証番号（10桁）");
            prompt.AppendLine("   - 支給決定障害者氏名");
            prompt.AppendLine("   - 支給決定に係る障害児氏名");
            prompt.AppendLine("   - 障害支援区分");
            prompt.AppendLine("   - 開始年月日");
            prompt.AppendLine("   - 終了年月日");
            prompt.AppendLine("   - 利用日数");
            prompt.AppendLine();
            prompt.AppendLine("3. 事業所信息：");
            prompt.AppendLine("   - 事業所番号");
            prompt.AppendLine("   - 事業者及びその事業所の名称");
            prompt.AppendLine("   - 地域区分");
            prompt.AppendLine("   - 旧身体療護施設区分（該当/未定可）");
            prompt.AppendLine("   - 精神科医療連携体制加算（東京可/未定可）");
            prompt.AppendLine();
            prompt.AppendLine("4. 基本報酬分（表格中的服务明细）：");
            prompt.AppendLine("   - サービスコード（包括完整的日期格式如241112）");
            prompt.AppendLine("   - サービス内容（如：福祉短期入所15）");
            prompt.AppendLine("   - 算定単価額");
            prompt.AppendLine("   - 利用日数");
            prompt.AppendLine("   - 当月算定額");
            prompt.AppendLine("   - 摘要（如：土対応、対応清み等）");
            prompt.AppendLine();
            prompt.AppendLine("5. 加算項目（下半部分的表格）：");
            prompt.AppendLine("   - サービスコード（如246080, 246081等）");
            prompt.AppendLine("   - 内容（如：短期医療連携体制加算IV1, IV2, IV3等）");
            prompt.AppendLine("   - 算定単価額");
            prompt.AppendLine("   - 利用日数");
            prompt.AppendLine("   - 当月算定額");
            prompt.AppendLine("   - 精神科医療連携体制加算");
            prompt.AppendLine();
            prompt.AppendLine("6. 合計金額：");
            prompt.AppendLine("   - 小計①");
            prompt.AppendLine("   - 小計②");
            prompt.AppendLine("   - 当月報加算請求額（①+②）");
            prompt.AppendLine("   - 最终总金额（円）");
            prompt.AppendLine();
            prompt.AppendLine("注意事项：");
            prompt.AppendLine("- 准确识别所有数字，保留原始格式（如金额的逗号分隔：11,756）");
            prompt.AppendLine("- 日期格式保持原样（如：2025/2/1）");
            prompt.AppendLine("- 对于空白或标记为\"---\"的字段，返回null");
            prompt.AppendLine("- 服务代码前的数字格式要完整识别（如241112, 246080等）");
            prompt.AppendLine("- 识别所有符号和标记（如勾选标记✓）");
            prompt.AppendLine();
            prompt.Append("直接返回纯JSON格式，不要使用markdown代码块，不要添加任何说明文字。");

            return prompt.ToString();
        }

        public static async Task<JObject> ProcessImageAsync(string imagePath)
        {
            // 编码图片
            var imageData = EncodeImageToBase64(imagePath);

            // 创建提示词
            string prompt = CreateOcrPrompt();

            // 调用API
            var apiResponse = await CallClaudeApiAsync(imageData.Item1, imageData.Item2, prompt);

            // 提取结果
            return ExtractJsonFromResponse(apiResponse);
        }

        private static async Task<JObject> CallClaudeApiAsync(string imageBase64, string mimeType, string prompt)
        {
            // 构建请求内容
            var requestContent = new
            {
                model = Model,
                max_tokens = 4000,
                messages = new[]
                {
                new
                {
                    role = "user",
                    content = new object[]
                    {
                        new
                        {
                            type = "image",
                            source = new
                            {
                                type = "base64",
                                media_type = mimeType,
                                data = imageBase64
                            }
                        },
                        new
                        {
                            type = "text",
                            text = prompt
                        }
                    }
                }
            }
            };

            // 序列化请求内容
            string jsonContent = JsonConvert.SerializeObject(requestContent);
            var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

            // 创建HTTP请求
            using (var request = new HttpRequestMessage(HttpMethod.Post, ApiUrl))
            {
                request.Headers.Add("x-api-key", ApiKey);
                request.Headers.Add("anthropic-version", "2023-06-01");
                request.Content = content;

                // 发送请求
                var response = await _httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();

                // 解析响应
                string responseBody = await response.Content.ReadAsStringAsync();
                return JObject.Parse(responseBody);
            }
        }

        private static JObject ExtractJsonFromResponse(JObject apiResponse)
        {
            // 提取响应文本
            string responseText = apiResponse["content"]?[0]?["text"]?.ToString();
            if (string.IsNullOrEmpty(responseText))
                throw new Exception("API响应中没有找到有效内容");

            // 尝试提取JSON块
            string jsonText = null;
            if (responseText.Contains("```json"))
            {
                int start = responseText.IndexOf("```json") + 7;
                int end = responseText.LastIndexOf("```");
                if (start > 6 && end > start)
                {
                    jsonText = responseText.Substring(start, end - start).Trim();
                }
            }
            else if (responseText.Contains("```"))
            {
                int start = responseText.IndexOf("```") + 3;
                int end = responseText.LastIndexOf("```");
                if (start > 2 && end > start)
                {
                    jsonText = responseText.Substring(start, end - start).Trim();
                }
            }
            else
            {
                jsonText = responseText.Trim();
            }

            // 解析JSON
            if (!string.IsNullOrEmpty(jsonText))
            {
                try
                {
                    return JObject.Parse(jsonText);
                }
                catch (JsonException ex)
                {
                    throw new JsonException($"JSON解析失败: {ex.Message}\n原始文本: {jsonText.Substring(0, Math.Min(jsonText.Length, 200))}...", ex);
                }
            }

            throw new Exception("无法从响应中提取有效的JSON内容");
        }

        public static void SaveResult(JObject result, string outputFilePath)
        {
            File.WriteAllText(outputFilePath, result.ToString(Formatting.Indented));
        }
    }
}