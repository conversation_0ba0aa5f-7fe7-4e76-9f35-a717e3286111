﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace NAVI.Utils
{
    public static class ReflectionExtensions
    {
        public static void SetProperty<T>(this T obj, string propertyName, object value)
        {
            var prop = obj.GetType().GetProperty(propertyName); // ✅ 正确获取实际类型属性
            if (prop != null && prop.CanWrite)
            {
                var convertedValue = Convert.ChangeType(value, prop.PropertyType);
                prop.SetValue(obj, convertedValue, null);
            }
        }

        public static string GetProperty(this object obj, string propertyName)
        {
            if (obj == null)
                throw new ArgumentNullException(nameof(obj));

            var prop = obj.GetType().GetProperty(propertyName, BindingFlags.Public | BindingFlags.Instance);
            if (prop == null)
                throw new ArgumentException($"Property '{propertyName}' not found.");

            return prop.GetValue(obj) == null ? "" : prop.GetValue(obj).ToString();
        }
    }
}
