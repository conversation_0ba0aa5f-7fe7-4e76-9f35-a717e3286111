<Window x:Class="NAVI.Windows.UserEditWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="用户编辑" Height="500" Width="450"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" x:Name="TitleTextBlock" Text="新增用户" FontSize="18" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- 表单内容 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- 职员番号 -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="120"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="職員番号:" VerticalAlignment="Center"/>
                    <TextBox Grid.Column="1" x:Name="EmployeeNumberTextBox" Height="30" VerticalContentAlignment="Center"/>
                </Grid>

                <!-- 部署名 -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="120"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="部署名:" VerticalAlignment="Center"/>
                    <TextBox Grid.Column="1" x:Name="DepartmentTextBox" Height="30" VerticalContentAlignment="Center"/>
                </Grid>

                <!-- 役职 -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="120"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="役職:" VerticalAlignment="Center"/>
                    <TextBox Grid.Column="1" x:Name="PositionTextBox" Height="30" VerticalContentAlignment="Center"/>
                </Grid>

                <!-- 氏名 -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="120"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="氏名:" VerticalAlignment="Center"/>
                    <TextBox Grid.Column="1" x:Name="NameTextBox" Height="30" VerticalContentAlignment="Center"/>
                </Grid>

                <!-- ID番号 -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="120"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="ID番号:" VerticalAlignment="Center"/>
                    <TextBox Grid.Column="1" x:Name="IdNumberTextBox" Height="30" VerticalContentAlignment="Center"/>
                </Grid>

                <!-- 密码 -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="120"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="パスワード:" VerticalAlignment="Center"/>
                    <PasswordBox Grid.Column="1" x:Name="PasswordBox" Height="30" VerticalContentAlignment="Center"/>
                </Grid>

                <!-- 确认密码 -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="120"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="パスワード確認:" VerticalAlignment="Center"/>
                    <PasswordBox Grid.Column="1" x:Name="ConfirmPasswordBox" Height="30" VerticalContentAlignment="Center"/>
                </Grid>

                <!-- 编辑模式下的密码修改选项 -->
                <Grid x:Name="PasswordChangeGrid" Margin="0,0,0,15" Visibility="Collapsed">
                    <CheckBox x:Name="ChangePasswordCheckBox" Content="パスワードを変更する" 
                              Checked="ChangePasswordCheckBox_Checked" Unchecked="ChangePasswordCheckBox_Unchecked"/>
                </Grid>
            </StackPanel>
        </ScrollViewer>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button x:Name="SaveButton" Content="保存" Width="80" Height="35" Margin="0,0,10,0" 
                    Click="SaveButton_Click" Style="{StaticResource PrimaryButtonStyle}"/>
            <Button x:Name="CancelButton" Content="キャンセル" Width="80" Height="35" 
                    Click="CancelButton_Click" Style="{StaticResource SecondaryButtonStyle}"/>
        </StackPanel>
    </Grid>
</Window>
