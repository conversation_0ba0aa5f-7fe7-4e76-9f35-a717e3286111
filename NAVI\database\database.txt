﻿CREATE TABLE ServiceCodeMaster (
    "サービスコード" TEXT,
    "サービス内容略称" TEXT,
    "障害支援区分" TEXT,
    "合成単位" TEXT,
    "級地コード" TEXT,
    "単位数単価" TEXT,
    "国費単価" TEXT,
    "旧身体療護" TEXT,
    "都単価" TEXT,
    "キーコード" TEXT,
    "都加算単価" TEXT
);

CREATE TABLE ServiceProviders (
    "No" INTEGER PRIMARY KEY AUTOINCREMENT,
    "事業者番号" TEXT,
    "郵便番号" TEXT,
    "所在地" TEXT,
    "事業者名称" TEXT,
    "代表者役職" TEXT,
    "代表者名" TEXT,
    "担当者氏名" TEXT,
    "連絡先" TEXT,
    "サービス種別" TEXT,
    "加算対象サービス" TEXT,
    "第三者評価結果" TEXT,
    "研修受講証明" TEXT,
    "利用者情報" TEXT
);

CREATE TABLE KokuhoRenData (
    No        INTEGER PRIMARY KEY AUTOINCREMENT,
    サービス提供年月  TEXT,
    請求年月日     TEXT,
    請求回数      TEXT,
    審査年月      TEXT,
    事業者コード    TEXT,
    事業者名称     TEXT,
    受給者番号     TEXT,
    受給者名称     TEXT,
    受給者名称カナ   TEXT,
    児童名称      TEXT,
    児童名称カナ    TEXT,
    身体        TEXT,
    知的        TEXT,
    精神        TEXT,
    難病        TEXT,
    単価障害程度区分  TEXT,
    障害支援区分    TEXT,
    サービスコード   TEXT,
    サービス名称    TEXT,
    算定時間      TEXT,
    回数        TEXT,
    算定時間×回数   TEXT,
    単位数       TEXT,
    サービス単位    TEXT,
    連合会審査区分名称 TEXT,
    審査区分名称    TEXT,
    返戻事由名称    TEXT,
    判定フラグ     TEXT,
    status    TEXT
);
CREATE TABLE RecipientsData (
    "No" INTEGER PRIMARY KEY AUTOINCREMENT,
    "登録日" TEXT,
    "事業者番号" TEXT,
    "事業者郵便番号" TEXT,
    "事業者住所" TEXT,
    "事業者名称" TEXT,
    "代表者名" TEXT,
    "代表者役職" TEXT,
    "サービス提供年月" TEXT,
    "明細書件数" TEXT,
    "請求金額" TEXT,
    "第三者評価" TEXT,
    "受給者番号" TEXT,
    "支給決定障害者氏名" TEXT,
    "支給決定に係る障害児氏名" TEXT,
    "障害支援区分" TEXT,
    "事業者名称2" TEXT,
    "地域区分" TEXT,
    "旧身体療護施設区分" TEXT,
    "精神科医療連携体制加算" TEXT,
    "開始年月日" TEXT,
    "終了年月日" TEXT,
    "利用日数全体" TEXT,
    "サービスコード" TEXT,
    "サービス内容" TEXT,
    "算定単価額" TEXT,
    "利用日数" TEXT,
    "当月算定額" TEXT,
    "摘要" TEXT,
    "status" TEXT
);

CREATE TABLE Users (
    "職員番号" TEXT,
    "部署名" TEXT,
    "役職" TEXT,
    "氏名" TEXT,
    "ID番号" TEXT
);