# 事业者Excel导入功能说明

## 功能概述

本功能实现了从事业者提供的Excel文件中读取数据，并自动匹配基准数据（事业者数据、服务代码数据），最终导入到受给者数据表中。

## 主要特性

### 1. 智能数据读取
- **多单元格拼接**：自动识别并拼接跨多个单元格的数据
- **精确位置定位**：根据Excel文件的固定格式精确读取各字段数据
- **公式计算支持**：自动计算Excel中的公式结果

### 2. 数据匹配功能
- **事业者匹配**：根据事业所番号和事业所名称匹配事业者数据
- **服务代码匹配**：根据服务代码和服务内容匹配服务代码数据
- **智能容错**：支持多种匹配方式，提高匹配成功率

### 3. 自动数据处理
- **日期格式化**：自动将各种日期格式统一为 yyyy/MM/dd 格式
- **负担额计算**：自动计算负担额（月额费用的10%）
- **受给者ID生成**：基于受给者证番号自动生成唯一ID

## 数据读取位置

根据提供的Excel截图，系统会从以下位置读取数据：

### 基本信息
| 字段名 | Excel位置 | 示例值 |
|--------|-----------|--------|
| 年月 | 行8, 列24-26 | 令和7年2月 |
| 受给者证番号 | 行10, 列25 | 1234567890 |
| 支给决定障害者氏名 | 行11, 列25 | 鈴木太郎 |
| 支给决定に係る障害児氏名 | 行13, 列25 | 鈴木太郎 |
| 事业所番号 | 行10, 列32 | 1234567890 |
| 事业者及びその事业所の名称 | 行11-12, 列32 | サポートハウス東京 |
| 障害支援区分 | 行15, 列32 | 1級地 |
| 开始年月日 | 行18, 列8 | 2025-2-1 |
| 终了年月日 | 行18, 列20 | 2025-2-28 |
| 利用日数 | 行18, 列32 | 28 |

### 服务明细
| 字段名 | Excel位置 | 示例值 |
|--------|-----------|--------|
| 服务代码 | 行21-30, 列B-D | 241111 |
| 服务内容 | 行21-30, 列I-P | 福祉短期入所Ⅰ６ |
| 单定价格 | 行21-30, 列Q | 10210 |
| 利用日数 | 行21-30, 列R | 28 |
| 当月费定额 | 行21-30, 列S-T | 285,880 |

## 使用方法

### 1. 准备测试文件
1. 点击"テストファイル作成"按钮
2. 选择保存位置，创建测试用的Excel文件
3. 可以基于此文件修改为实际数据

### 2. 导入Excel文件
1. 点击"選択文件"按钮选择要导入的Excel文件
2. 支持多文件选择（最多200个文件）
3. 点击"一括ファイル取込実行"开始导入

### 3. 查看导入结果
- 导入进度会实时显示
- 成功/失败的文件数量会统计显示
- 导入的数据会自动保存到受给者服务信息表中

## 数据匹配规则

### 事业者数据匹配
1. **优先匹配**：事业者编号 = 事业所番号
2. **备选匹配**：事业者名称包含事业所名称

### 服务代码数据匹配
1. **优先匹配**：服务代码完全一致
2. **备选匹配**：服务名称包含服务内容

## 导入数据结构

导入到受给者服务信息表的数据包括：

| 字段名 | 数据来源 | 说明 |
|--------|----------|------|
| NO | 自动生成 | 行号 |
| 受给者ID | 自动生成 | R+受给者证番号 |
| 受给者姓名 | Excel文件 | 支给决定障害者氏名 |
| 生年月日 | 手动补充 | Excel中无此信息 |
| 性别 | 手动补充 | Excel中无此信息 |
| 住所 | 事业者数据 | 匹配的事业者地址 |
| 电话番号 | 事业者数据 | 匹配的事业者联系方式 |
| 服务代码 | Excel文件 | 服务明细中的代码 |
| 服务名称 | 匹配结果 | 优先使用匹配的服务名称 |
| 利用开始日 | Excel文件 | 格式化后的开始日期 |
| 利用结束日 | Excel文件 | 格式化后的结束日期 |
| 月利用回数 | Excel文件 | 服务明细中的利用日数 |
| 单价 | Excel文件 | 服务明细中的单定价格 |
| 月额费用 | Excel文件 | 服务明细中的当月费定额 |
| 负担额 | 自动计算 | 月额费用的10% |
| 状态 | 固定值 | "利用中" |
| 备注 | 自动生成 | 包含事业所信息等 |

## 注意事项

1. **Excel格式要求**：Excel文件必须严格按照指定格式排列数据
2. **基准数据完整性**：确保事业者数据和服务代码数据已正确导入
3. **文件大小限制**：建议单个Excel文件不超过10MB
4. **并发限制**：避免同时导入大量文件，建议分批处理
5. **数据备份**：导入前建议备份现有数据

## 错误处理

- **文件格式错误**：系统会跳过无法识别的文件
- **数据匹配失败**：未匹配的数据会使用原始值
- **计算错误**：计算失败的字段会设置为默认值
- **保存失败**：会显示具体错误信息

## 扩展功能

系统设计具有良好的扩展性，可以轻松添加：
- 新的数据字段读取
- 更复杂的匹配规则
- 自定义计算逻辑
- 数据验证规则
