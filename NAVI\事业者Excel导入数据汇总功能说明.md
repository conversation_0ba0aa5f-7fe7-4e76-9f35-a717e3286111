# 事业者Excel导入数据汇总整理功能说明

## 功能概述

本功能实现了从事业者提供的Excel文件中读取数据，经过智能匹配、汇总整理后，导入到标准化的受给者数据表中。系统会自动处理数据格式转换、字段映射和数据验证。

## 数据汇总整理流程

### 1. 数据读取阶段
- **精确位置读取**：根据Excel文件的固定格式，精确读取各个字段数据
- **多单元格拼接**：自动识别并拼接跨多个单元格的数据（如年月信息、事业所名称等）
- **数据验证**：验证必要字段的完整性和格式正确性

### 2. 数据匹配阶段
- **事业者数据匹配**：
  - 优先按事業者番号精确匹配
  - 备选按事業者名称模糊匹配
  - 自动补充事业者相关信息（郵便番号、住所、代表者等）

- **服务代码数据匹配**：
  - 优先按服务代码精确匹配
  - 备选按服务内容模糊匹配
  - 获取标准化的服务名称和相关信息

### 3. 数据汇总整理阶段
- **服务明细汇总**：按服务代码分组合并同类服务
- **数值计算**：
  - 汇总利用日数
  - 计算平均单价
  - 汇总月额费用
  - 统计明细书件数

- **数据格式化**：
  - 日期格式统一为 yyyy/MM/dd
  - 数值字段转换为适当的数据类型
  - 清理和标准化文本字段

### 4. 数据导入阶段
- **字段映射**：将Excel数据映射到标准化的数据库字段
- **行号生成**：自动生成连续的行号
- **状态设置**：设置默认状态为"利用中"

## 导入数据字段结构

根据您提供的字段结构，系统会创建包含以下字段的记录：

| 字段名 | 数据来源 | 说明 |
|--------|----------|------|
| No | 自动生成 | 连续行号 |
| 登録日 | 系统生成 | 当前日期 |
| 事業者番号 | Excel文件 | 事业所番号 |
| 事業者郵便番号 | 匹配结果 | 从事业者数据中获取 |
| 事業者住所 | 匹配结果 | 从事业者数据中获取 |
| 事業者名称 | Excel文件 | 事业者及びその事业所の名称 |
| 代表者名 | 匹配结果 | 从事业者数据中获取 |
| 代表者役職 | 匹配结果 | 从事业者数据中获取 |
| サービス提供年月 | Excel文件 | 年月信息 |
| 明細書件数 | 计算生成 | 服务明细数量 |
| 請求金額 | Excel文件 | 当月费定额 |
| 第三者評価 | 暂时为空 | 待后续补充 |
| 受給者番号 | Excel文件 | 受给者证番号 |
| 支給決定障害者氏名 | Excel文件 | 支给决定障害者氏名 |
| 支給決定に係る障害児氏名 | Excel文件 | 支给决定に係る障害児氏名 |
| 障害支援区分 | Excel文件 | 障害支援区分 |
| 事業者名称2 | Excel文件 | 备用事业者名称 |
| 地域区分 | Excel文件 | 地域区分 |
| 旧身体療護施設区分 | 暂时为空 | 待后续补充 |
| 精神科医療連携体制加算 | Excel文件 | 精神科医療連携体制加算 |
| 開始年月日 | Excel文件 | 格式化后的开始日期 |
| 終了年月日 | Excel文件 | 格式化后的结束日期 |
| 利用日数全体 | Excel文件 | 总利用日数 |
| サービスコード | Excel文件 | 服务代码 |
| サービス内容 | 匹配结果 | 优先使用匹配的标准服务名称 |
| 算定単価額 | Excel文件 | 单定价格 |
| 利用日数 | Excel文件 | 服务明细中的利用日数 |
| 当月算定額 | Excel文件 | 当月费定额 |
| 摘要 | 自动生成 | 包含地域区分、障害支援区分等信息 |
| status | 固定值 | "利用中" |

## 数据汇总特性

### 1. 智能去重合并
- 按服务代码自动分组
- 合并相同服务的多个记录
- 保留原始记录数量信息

### 2. 数据完整性保证
- 必要字段验证
- 数据格式检查
- 匹配失败警告

### 3. 错误处理机制
- 数据验证失败时的详细错误信息
- 匹配失败时的备选处理方案
- 导入过程中的异常处理

### 4. 进度跟踪
- 实时显示导入进度
- 详细的成功/失败统计
- 匹配结果汇总报告

## 使用方法

1. **准备Excel文件**：确保Excel文件格式符合标准模板
2. **选择文件**：通过界面选择要导入的Excel文件
3. **执行导入**：点击导入按钮开始处理
4. **查看结果**：检查导入结果和汇总报告

## 注意事项

1. **Excel格式要求**：必须严格按照指定格式排列数据
2. **基准数据完整性**：确保事业者数据和服务代码数据已正确导入
3. **数据备份**：导入前建议备份现有数据
4. **字段映射**：系统会自动处理字段名称的差异

## 扩展功能

- 支持批量文件导入
- 数据导入前预览
- 自定义字段映射规则
- 导入历史记录查询

这个汇总整理功能确保了从Excel文件到数据库的数据一致性和完整性，大大提高了数据处理的效率和准确性。
